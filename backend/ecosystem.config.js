module.exports = {
  apps: [{
    name: "dianping-backend",
    script: "./src/app.js",
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: "1G",
    
    // 日志配置
    error_file: "/var/log/dianping-backend/error.log",
    out_file: "/var/log/dianping-backend/out.log",
    log_file: "/var/log/dianping-backend/combined.log",
    log_date_format: "YYYY-MM-DD HH:mm:ss Z",
    max_size: "100M",
    retain: 10,
    merge_logs: true,
    
    // 生产环境配置
    env: {
      NODE_ENV: "production",
      PORT: 8011,
      DB_ENV: "remote",
      MONGODB_URI: "***************************************************************************",
      SESSION_SECRET: "dianping-super-secret-session-key-2024",
      CORS_ORIGIN: "*", // 允许所有来源
      UPLOAD_PATH: "/var/uploads/dianping",
      
      // 速率限制配置
      RATE_LIMIT_WINDOW_MS: "900000", // 15分钟
      RATE_LIMIT_MAX_REQUESTS: "100",
      
      // 其他配置
      LOG_LEVEL: "info"
    },
    
    // 开发环境配置
    env_development: {
      NODE_ENV: "development",
      PORT: 8011,
      DB_ENV: "local",
      MONGODB_URI: "***************************************************************************",
      SESSION_SECRET: "dev-session-secret",
      CORS_ORIGIN: "*", // 允许所有来源
      UPLOAD_PATH: "./uploads",
      
      // 开发环境速率限制更宽松
      RATE_LIMIT_WINDOW_MS: "900000",
      RATE_LIMIT_MAX_REQUESTS: "1000",
      
      LOG_LEVEL: "debug"
    },
    
    // 测试环境配置
    env_test: {
      NODE_ENV: "test",
      PORT: 8012,
      DB_ENV: "test",
      MONGODB_URI: "***************************************************************************",
      SESSION_SECRET: "test-session-secret",
      CORS_ORIGIN: "*", // 允许所有来源
      UPLOAD_PATH: "./test-uploads",
      LOG_LEVEL: "error"
    },
    
    // 进程管理配置
    min_uptime: "10s",
    max_restarts: 10,
    restart_delay: 4000,
    
    // 健康检查
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true,
    
    // 集群模式配置（可选）
    exec_mode: "fork", // 或 "cluster"
    
    // 内存和CPU限制
    max_memory_restart: "1G",
    node_args: "--max-old-space-size=1024"
  }]
};
