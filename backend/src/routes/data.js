const express = require('express');
const dataService = require('../services/dataService');

const router = express.Router();

/**
 * 认证中间件
 */
const requireAuth = (req, res, next) => {
  console.log('认证检查 - Session ID:', req.sessionID);
  console.log('认证检查 - Session数据:', req.session);
  console.log('认证检查 - MerchantId:', req.session.merchantId);
  console.log('认证检查 - IsAuthenticated:', req.session.isAuthenticated);

  if (!req.session.merchantId) {
    console.log('认证失败 - 没有merchantId');
    return res.status(401).json({
      success: false,
      message: '请先登录'
    });
  }
  console.log('认证成功 - 继续处理请求');
  next();
};

/**
 * 获取会话顾客信息
 * POST /api/data/chat-groups
 */
router.post('/chat-groups', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const cookies = req.session.cookies;
    const { shopId, fromDate, toDate } = req.body;

    if (!merchantId || !cookies) {
      return res.status(400).json({
        success: false,
        message: '认证信息不完整'
      });
    }

    if (!shopId || !fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: '请提供店铺ID和时间范围'
      });
    }

    console.log(`开始提取店铺 ${shopId} 从 ${fromDate} 到 ${toDate} 的数据`);

    const result = await dataService.extractCustomerDetails(shopId, cookies, fromDate, toDate);

    res.json(result);
  } catch (error) {
    console.error('获取会话列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 重新设计的数据提取接口（三步API调用流程）
 * POST /api/data/extract
 */
router.post('/extract', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const cookies = req.session.cookies;

    if (!merchantId || !cookies) {
      return res.status(400).json({
        success: false,
        message: '认证信息不完整'
      });
    }

    // 执行三步数据提取流程
    const result = await dataService.extractCustomerDetails(merchantId, cookies);
    res.json(result);
  } catch (error) {
    console.error('数据提取失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取历史数据
 * GET /api/data/history
 */
router.get('/history', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const { page = 1, pageSize = 50 } = req.query;

    // 从数据库获取历史数据
    const result = await dataService.getHistoryData(merchantId, { page: parseInt(page), pageSize: parseInt(pageSize) });
    res.json(result);
  } catch (error) {
    console.error('获取历史数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取数据统计信息
 * GET /api/data/stats
 */
router.get('/stats', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;

    if (!merchantId) {
      return res.status(400).json({
        success: false,
        message: '商户ID不能为空'
      });
    }

    const result = await dataService.getSimpleStats(merchantId);
    res.json(result);
  } catch (error) {
    console.error('获取统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 删除数据
 * DELETE /api/data/:id
 */
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const merchantId = req.session.merchantId;

    const CustomerData = require('../models/CustomerData');
    
    // 验证数据所有权
    const data = await CustomerData.findOne({ _id: id, merchantId });
    if (!data) {
      return res.status(404).json({
        success: false,
        message: '数据不存在或无权限删除'
      });
    }

    await CustomerData.findByIdAndDelete(id);

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 批量删除数据
 * POST /api/data/batch-delete
 */
router.post('/batch-delete', requireAuth, async (req, res) => {
  try {
    const { ids } = req.body;
    const merchantId = req.session.merchantId;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的数据ID列表'
      });
    }

    const CustomerData = require('../models/CustomerData');
    
    // 批量删除（只删除属于当前商户的数据）
    const result = await CustomerData.deleteMany({
      _id: { $in: ids },
      merchantId
    });

    res.json({
      success: true,
      deletedCount: result.deletedCount,
      message: `成功删除 ${result.deletedCount} 条数据`
    });
  } catch (error) {
    console.error('批量删除数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
