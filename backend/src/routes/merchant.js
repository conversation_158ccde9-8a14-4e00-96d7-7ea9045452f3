const express = require('express');
const authService = require('../services/authService');

const router = express.Router();

/**
 * 认证中间件
 */
const requireAuth = (req, res, next) => {
  if (!req.session.merchantId) {
    return res.status(401).json({
      success: false,
      message: '请先登录'
    });
  }
  next();
};

/**
 * 获取商户信息
 * GET /api/merchant/info/:merchantId
 */
router.get('/info/:merchantId', requireAuth, async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    const result = await authService.getMerchantAuth(merchantId);
    
    if (result.success) {
      // 不返回敏感信息
      const { cookies, authToken, ...safeInfo } = result.merchant;
      res.json({
        success: true,
        merchant: safeInfo,
        message: result.message
      });
    } else {
      res.status(404).json(result);
    }
  } catch (error) {
    console.error('获取商户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 更新商户信息
 * PUT /api/merchant/info/:merchantId
 */
router.put('/info/:merchantId', requireAuth, async (req, res) => {
  try {
    const { merchantId } = req.params;
    const { merchantName, stores, settings } = req.body;

    // 验证权限
    if (req.session.merchantId !== merchantId) {
      return res.status(403).json({
        success: false,
        message: '无权限操作此商户'
      });
    }

    // 获取现有商户信息
    const existingResult = await authService.getMerchantAuth(merchantId);
    if (!existingResult.success) {
      return res.status(404).json(existingResult);
    }

    // 更新商户信息
    const updateData = {
      ...existingResult.merchant,
      merchantName: merchantName || existingResult.merchant.merchantName,
      stores: stores || existingResult.merchant.stores
    };

    const result = await authService.saveMerchantAuth(updateData);
    
    if (result.success) {
      // 更新会话信息
      req.session.merchantName = updateData.merchantName;
    }

    res.json(result);
  } catch (error) {
    console.error('更新商户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取商户门店列表
 * GET /api/merchant/:merchantId/stores
 */
router.get('/:merchantId/stores', requireAuth, async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    const result = await authService.getMerchantAuth(merchantId);
    
    if (result.success) {
      res.json({
        success: true,
        stores: result.merchant.stores || [],
        message: '获取门店列表成功'
      });
    } else {
      res.status(404).json(result);
    }
  } catch (error) {
    console.error('获取门店列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 测试商户认证状态
 * GET /api/merchant/:merchantId/test-auth
 */
router.get('/:merchantId/test-auth', requireAuth, async (req, res) => {
  try {
    const { merchantId } = req.params;
    
    // 获取商户认证信息
    const authResult = await authService.getMerchantAuth(merchantId);
    if (!authResult.success) {
      return res.status(404).json(authResult);
    }

    // 验证认证信息是否仍然有效
    const validateResult = await authService.validateMerchantAuth(
      authResult.merchant.cookies,
      merchantId
    );

    res.json({
      success: validateResult.success,
      isValid: validateResult.success,
      message: validateResult.success ? '认证信息有效' : '认证信息已失效，请重新登录',
      lastLoginTime: authResult.merchant.lastLoginTime
    });
  } catch (error) {
    console.error('测试认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
