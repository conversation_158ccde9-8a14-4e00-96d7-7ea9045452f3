const express = require('express');
const authService = require('../services/authService');
const securityService = require('../services/securityService');

const router = express.Router();

// 安全中间件
const loginRateLimit = securityService.createLoginRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次登录尝试
  message: '登录尝试次数过多，请15分钟后再试'
});

const apiRateLimit = securityService.createApiRateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 最多60次API请求
  message: 'API请求过于频繁，请稍后再试'
});

// ==================== 多Cookie管理API ====================

/**
 * 获取Cookie配置列表
 * GET /api/auth/cookie-configs
 */
router.get('/cookie-configs', async (req, res) => {
  try {
    const { merchantId } = req.query;
    const result = await authService.getCookieConfigs(merchantId);

    res.json(result);
  } catch (error) {
    console.error('获取Cookie配置列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 创建Cookie配置
 * POST /api/auth/cookie-configs
 */
router.post('/cookie-configs', async (req, res) => {
  try {
    const { name, description, cookies, password } = req.body;

    if (!name || !cookies || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的配置信息（名称、Cookie、密码）'
      });
    }

    // 从Cookie中提取商户ID
    const merchantId = extractMerchantId(cookies);
    if (!merchantId) {
      return res.status(400).json({
        success: false,
        message: '无法从Cookie中提取商户ID，请检查Cookie信息是否完整'
      });
    }

    const result = await authService.createCookieConfig({
      name,
      description,
      cookies,
      password,
      merchantId
    });

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('创建Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 使用Cookie配置进行认证
 * POST /api/auth/authenticate-with-config
 */
router.post('/authenticate-with-config', loginRateLimit, async (req, res) => {
  try {
    const { configId, password } = req.body;

    if (!configId || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供配置ID和密码'
      });
    }

    const result = await authService.authenticateWithConfig(configId, password);

    if (result.success) {
      // 保存到会话
      req.session.merchantId = result.data.merchantId;
      req.session.cookies = result.data.cookies;
      req.session.isAuthenticated = true;
      req.session.currentConfigId = result.data.configId;
      req.session.currentConfigName = result.data.configName;

      res.json({
        success: true,
        merchantId: result.data.merchantId,
        merchantName: result.data.merchantName,
        configName: result.data.configName,
        message: result.message
      });
    } else {
      res.status(401).json(result);
    }
  } catch (error) {
    console.error('Cookie配置认证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 使用密码进行认证（自动匹配配置）
 * POST /api/auth/authenticate-with-password
 */
router.post('/authenticate-with-password', loginRateLimit, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请提供密码'
      });
    }

    const result = await authService.authenticateWithPassword(password);

    if (result.success) {
      // 保存到会话
      req.session.merchantId = result.data.merchantId;
      req.session.cookies = result.data.cookies;
      req.session.isAuthenticated = true;
      req.session.currentConfigId = result.data.configId;
      req.session.currentConfigName = result.data.configName;

      res.json({
        success: true,
        merchantId: result.data.merchantId,
        merchantName: result.data.merchantName,
        configName: result.data.configName,
        message: result.message
      });
    } else {
      res.status(401).json(result);
    }
  } catch (error) {
    console.error('密码认证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 更新Cookie配置
 * PUT /api/auth/cookie-configs/:configId
 */
router.put('/cookie-configs/:configId', async (req, res) => {
  try {
    const { configId } = req.params;
    const { name, description, cookies, newPassword, password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请提供当前密码'
      });
    }

    const updateData = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (cookies) updateData.cookies = cookies;
    if (newPassword) updateData.newPassword = newPassword;

    const result = await authService.updateCookieConfig(configId, updateData, password);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('更新Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 删除Cookie配置
 * DELETE /api/auth/cookie-configs/:configId
 */
router.delete('/cookie-configs/:configId', async (req, res) => {
  try {
    const { configId } = req.params;
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请提供密码'
      });
    }

    const result = await authService.deleteCookieConfig(configId, password);

    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('删除Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 验证Cookie配置有效性
 * POST /api/auth/cookie-configs/:configId/validate
 */
router.post('/cookie-configs/:configId/validate', async (req, res) => {
  try {
    const { configId } = req.params;
    const result = await authService.validateCookieConfig(configId);

    res.json(result);
  } catch (error) {
    console.error('验证Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取最近使用的Cookie配置
 * GET /api/auth/cookie-configs/recent
 */
router.get('/cookie-configs/recent', async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    const result = await authService.getRecentlyUsedConfigs(parseInt(limit));

    res.json(result);
  } catch (error) {
    console.error('获取最近使用配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 批量验证Cookie配置
 * POST /api/auth/cookie-configs/batch-validate
 */
router.post('/cookie-configs/batch-validate', async (req, res) => {
  try {
    const { configIds } = req.body;

    if (!Array.isArray(configIds) || configIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的配置ID数组'
      });
    }

    const result = await authService.batchValidateCookieConfigs(configIds);
    res.json(result);
  } catch (error) {
    console.error('批量验证Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取最佳Cookie配置
 * GET /api/auth/cookie-configs/optimal
 */
router.get('/cookie-configs/optimal', async (req, res) => {
  try {
    const { merchantId } = req.query;
    const result = await authService.getOptimalCookieConfig(merchantId);

    res.json(result);
  } catch (error) {
    console.error('获取最佳Cookie配置失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 验证密码强度
 * POST /api/auth/validate-password-strength
 */
router.post('/validate-password-strength', apiRateLimit, async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请提供密码'
      });
    }

    const result = securityService.validatePasswordStrength(password);

    res.json({
      success: true,
      data: result,
      message: '密码强度验证完成'
    });
  } catch (error) {
    console.error('密码强度验证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 生成安全密码
 * POST /api/auth/generate-password
 */
router.post('/generate-password', apiRateLimit, async (req, res) => {
  try {
    const {
      length = 12,
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = true,
      includeSymbols = false,
      excludeSimilar = true
    } = req.body;

    const password = securityService.generateSecurePassword(length, {
      includeUppercase,
      includeLowercase,
      includeNumbers,
      includeSymbols,
      excludeSimilar
    });

    const strengthCheck = securityService.validatePasswordStrength(password);

    res.json({
      success: true,
      data: {
        password,
        strength: strengthCheck
      },
      message: '安全密码生成成功'
    });
  } catch (error) {
    console.error('生成安全密码失败:', error);
    res.status(500).json({
      success: false,
      message: '生成安全密码失败'
    });
  }
});

// ==================== 兼容性API ====================

/**
 * 验证商户认证信息（简化版）
 * POST /api/auth/validate
 */
router.post('/validate', loginRateLimit, async (req, res) => {
  try {
    const { cookies } = req.body;

    if (!cookies) {
      return res.status(400).json({
        success: false,
        message: '请提供Cookie信息'
      });
    }

    // 从Cookie中提取商户ID
    const merchantId = extractMerchantId(cookies);
    if (!merchantId) {
      return res.status(400).json({
        success: false,
        message: '无法从Cookie中提取商户ID，请检查Cookie信息是否完整'
      });
    }

    const result = await authService.validateMerchantAuth(cookies, merchantId);

    if (result.success) {
      // 保存到数据库
      const saveResult = await authService.saveMerchantAuth({
        merchantId,
        merchantName: `商户${merchantId}`,
        cookies,
        stores: result.merchantInfo?.stores || []
      });

      if (saveResult.success) {
        // 保存到会话
        req.session.merchantId = merchantId;
        req.session.cookies = cookies;
        req.session.isAuthenticated = true;

        console.log('会话保存成功 - Session ID:', req.sessionID);
        console.log('会话保存成功 - MerchantId:', req.session.merchantId);
        console.log('会话保存成功 - IsAuthenticated:', req.session.isAuthenticated);

        res.json({
          success: true,
          merchantId,
          message: '验证成功并已保存'
        });
      } else {
        res.status(500).json({
          success: false,
          message: '验证成功但保存失败'
        });
      }
    } else {
      res.status(401).json(result);
    }
  } catch (error) {
    console.error('验证认证信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 保存商户认证信息
 * POST /api/auth/save
 */
router.post('/save', async (req, res) => {
  try {
    const { merchantId, merchantName, cookies, authToken, stores } = req.body;

    if (!merchantId || !merchantName || !cookies) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的商户信息'
      });
    }

    const result = await authService.saveMerchantAuth({
      merchantId,
      merchantName,
      cookies,
      authToken,
      stores
    });

    if (result.success) {
      // 保存到会话
      req.session.merchantId = merchantId;
      req.session.merchantName = merchantName;
    }

    res.json(result);
  } catch (error) {
    console.error('保存认证信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取商户列表
 * GET /api/auth/merchants
 */
router.get('/merchants', async (req, res) => {
  try {
    const result = await authService.getMerchantList();
    res.json(result);
  } catch (error) {
    console.error('获取商户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 自动验证已保存的认证信息（支持多Cookie配置）
 * GET /api/auth/auto-validate
 */
router.get('/auto-validate', async (req, res) => {
  try {
    // 首先尝试获取最佳Cookie配置
    const optimalResult = await authService.getOptimalCookieConfig();

    if (optimalResult.success) {
      // 尝试使用最佳配置进行自动认证（需要密码，这里暂时跳过）
      res.json({
        success: false,
        isAuthenticated: false,
        hasConfigs: true,
        optimalConfig: optimalResult.data,
        message: '找到可用配置，请选择配置并输入密码登录'
      });
    } else {
      // 回退到旧的自动验证方式（兼容性）
      const result = await authService.autoValidateAuth();

      if (result.success) {
        // 保存到会话
        req.session.merchantId = result.merchant.merchantId;
        req.session.cookies = result.merchant.cookies;
        req.session.isAuthenticated = true;

        res.json({
          success: true,
          isAuthenticated: true,
          merchantId: result.merchant.merchantId,
          merchantName: result.merchant.merchantName,
          message: result.message
        });
      } else {
        res.json({
          success: false,
          isAuthenticated: false,
          hasConfigs: false,
          message: result.message
        });
      }
    }
  } catch (error) {
    console.error('自动验证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取当前会话信息（支持多Cookie配置）
 * GET /api/auth/session
 */
router.get('/session', (req, res) => {
  try {
    if (req.session.isAuthenticated && req.session.merchantId) {
      res.json({
        success: true,
        isAuthenticated: true,
        merchantId: req.session.merchantId,
        currentConfigId: req.session.currentConfigId || null,
        currentConfigName: req.session.currentConfigName || null,
        message: '会话有效'
      });
    } else {
      res.json({
        success: true,
        isAuthenticated: false,
        message: '未登录'
      });
    }
  } catch (error) {
    console.error('获取会话信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 退出登录
 * POST /api/auth/logout
 */
router.post('/logout', (req, res) => {
  try {
    req.session.destroy((err) => {
      if (err) {
        console.error('销毁会话失败:', err);
        return res.status(500).json({
          success: false,
          message: '退出登录失败'
        });
      }

      res.clearCookie('connect.sid');
      res.json({
        success: true,
        message: '退出登录成功'
      });
    });
  } catch (error) {
    console.error('退出登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});



// 从Cookie中提取商户ID的辅助函数
function extractMerchantId(cookieString) {
  const match = cookieString.match(/mpmerchant_portal_shopid=([^;]+)/);
  return match ? match[1] : null;
}

module.exports = router;
