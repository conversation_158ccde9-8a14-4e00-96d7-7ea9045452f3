const express = require('express');
const path = require('path');
const exportService = require('../services/exportService');

const router = express.Router();

/**
 * 认证中间件
 */
const requireAuth = (req, res, next) => {
  if (!req.session.merchantId) {
    return res.status(401).json({
      success: false,
      message: '请先登录'
    });
  }
  next();
};

/**
 * 导出Excel文件
 * POST /api/export/excel
 */
router.post('/excel', requireAuth, async (req, res) => {
  try {
    // 设置更长的超时时间（10分钟）
    req.setTimeout(600000);
    res.setTimeout(600000);

    const merchantId = req.session.merchantId;
    const cookies = req.session.cookies;
    const filters = req.body;

    if (!cookies) {
      return res.status(400).json({
        success: false,
        message: '认证信息不完整，请重新登录'
      });
    }

    console.log('开始Excel导出，预计需要较长时间...');
    const result = await exportService.exportToExcel(merchantId, cookies, filters);
    
    if (result.success) {
      // 设置响应头，触发文件下载
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(result.filename)}`);
      
      // 发送文件
      res.sendFile(result.filepath, (err) => {
        if (err) {
          console.error('发送文件失败:', err);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: '文件下载失败'
            });
          }
        } else {
          // 文件发送成功后，异步清理临时文件
          setTimeout(() => {
            exportService.cleanupTempFile(result.filepath);
          }, 5000);
        }
      });
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('导出Excel失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取导出统计信息
 * GET /api/export/stats
 */
router.get('/stats', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    
    const result = await exportService.getExportStats(merchantId);
    res.json(result);
  } catch (error) {
    console.error('获取导出统计失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取店铺列表
 * GET /api/export/shops
 */
router.get('/shops', requireAuth, async (req, res) => {
  try {
    const cookies = req.session.cookies;

    if (!cookies) {
      return res.status(400).json({
        success: false,
        message: '认证信息不完整，请重新登录'
      });
    }

    console.log('开始获取店铺列表...');

    // 获取店铺列表
    const dataService = require('../services/dataService');
    const shopListResult = await dataService.getShopList(cookies);

    if (!shopListResult.success) {
      return res.status(400).json({
        success: false,
        message: `获取店铺列表失败: ${shopListResult.message}`
      });
    }

    res.json({
      success: true,
      data: shopListResult.data,
      total: shopListResult.total,
      message: shopListResult.message
    });

  } catch (error) {
    console.error('获取店铺列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 提取数据并返回（前端生成Excel）
 * POST /api/export/extract-data
 */
router.post('/extract-data', requireAuth, async (req, res) => {
  try {
    // 设置更长的超时时间（10分钟）
    req.setTimeout(600000);
    res.setTimeout(600000);

    const merchantId = req.session.merchantId;
    const cookies = req.session.cookies;
    const { shopId, fromDate, toDate } = req.body;

    if (!cookies) {
      return res.status(400).json({
        success: false,
        message: '认证信息不完整，请重新登录'
      });
    }

    // 验证参数
    if (!shopId) {
      return res.status(400).json({
        success: false,
        message: '请提供店铺ID'
      });
    }

    if (!fromDate || !toDate) {
      return res.status(400).json({
        success: false,
        message: '请提供开始日期和结束日期'
      });
    }

    console.log('开始提取数据...', { shopId, fromDate, toDate });

    // 执行数据提取流程
    const dataService = require('../services/dataService');
    const extractResult = await dataService.extractCustomerDetails(shopId, cookies, fromDate, toDate);

    if (!extractResult.success) {
      return res.status(400).json({
        success: false,
        message: `数据提取失败: ${extractResult.message}`
      });
    }

    // 格式化门店名称的辅助函数
    const formatStoreName = (storeName) => {
      if (!storeName) return '';

      // 匹配括号中的内容，支持中文括号（）和英文括号()
      const match = storeName.match(/[（(]([^）)]+)[）)]/);
      if (match && match[1]) {
        return match[1];
      }

      // 如果没有括号，返回原始名称
      return storeName;
    };

    // 格式化数据供前端使用
    const excelData = extractResult.data.map(item => ({
      '日期': item.date || '',
      '时间': item.time || '',
      '私信来源': item.messageSource || '',
      '客服人员': item.customerService || '',
      '门店': formatStoreName(item.store) || '',
      '客户类型': item.customerType || '',
      '是否有效': '',
      '第一句话': item.firstMessage || '',
      '咨询类型': item.consultationType || '',
      '咨询渠道': item.consultationChannel || '',
      '平台昵称': item.platformNickname || '',
      '联系方式': item.contactInfo || '',
      '咨询分类1': item.consultationLevel1 || '',
      '咨询分类2': item.consultationLevel2 || '',
      '咨询分类3': item.consultationLevel3 || '',
      '添加微信': item.addWechat || '',
      '预约时间': item.appointmentTime || '',
      '备注': item.remarks || '',
      '留联数': item.contactCount || ''
    }));

    res.json({
      success: true,
      data: excelData,
      total: extractResult.total,
      successCount: extractResult.successCount,
      errorCount: extractResult.errorCount,
      errors: extractResult.errors,
      message: `数据提取完成！成功: ${extractResult.successCount}，失败: ${extractResult.errorCount}`
    });

  } catch (error) {
    console.error('提取数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 预览导出数据
 * POST /api/export/preview
 */
router.post('/preview', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const filters = req.body;

    // 限制预览数据量
    const previewFilters = {
      ...filters,
      page: 1,
      pageSize: 100
    };

    const dataService = require('../services/dataService');
    const result = await dataService.getHistoricalData(merchantId, previewFilters);

    if (result.success) {
      // 格式化预览数据
      const previewData = result.data.map(item => ({
        date: item.date || '',
        time: item.time || '',
        messageSource: item.messageSource || '',
        customerService: item.customerService || '',
        store: item.store || '',
        customerType: item.customerType || '',
        isValid: item.isValid || '',
        firstMessage: item.firstMessage ? item.firstMessage.substring(0, 50) + '...' : '',
        consultationType: item.consultationType || '',
        consultationChannel: item.consultationChannel || '',
        platformNickname: item.platformNickname || '',
        contactInfo: item.contactInfo || '',
        consultationLevel1: item.consultationLevel1 || '',
        consultationLevel2: item.consultationLevel2 || '',
        consultationLevel3: item.consultationLevel3 || '',
        addWechat: item.addWechat || '',
        appointmentTime: item.appointmentTime || '',
        remarks: item.remarks ? item.remarks.substring(0, 30) + '...' : '',
        contactCount: item.contactCount || ''
      }));

      res.json({
        success: true,
        data: previewData,
        total: result.total,
        message: '获取预览数据成功'
      });
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('获取预览数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 获取导出历史记录
 * GET /api/export/history
 */
router.get('/history', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const { page = 1, pageSize = 20 } = req.query;

    const CustomerData = require('../models/CustomerData');

    // 获取导出历史（按导出时间分组）
    const history = await CustomerData.aggregate([
      {
        $match: {
          merchantId,
          status: 'exported',
          exportedAt: { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d %H:%M',
              date: '$exportedAt'
            }
          },
          exportId: { $first: '$_id' }, // 用于重新下载的ID
          count: { $sum: 1 },
          exportedAt: { $first: '$exportedAt' },
          stores: { $addToSet: '$store' },
          dateRange: {
            $push: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$collectedAt'
              }
            }
          }
        }
      },
      {
        $addFields: {
          dateRangeStr: {
            $let: {
              vars: {
                uniqueDates: { $setUnion: ['$dateRange', []] }
              },
              in: {
                $cond: {
                  if: { $eq: [{ $size: '$$uniqueDates' }, 1] },
                  then: { $arrayElemAt: ['$$uniqueDates', 0] },
                  else: {
                    $concat: [
                      { $arrayElemAt: ['$$uniqueDates', 0] },
                      ' 至 ',
                      { $arrayElemAt: ['$$uniqueDates', -1] }
                    ]
                  }
                }
              }
            }
          }
        }
      },
      { $sort: { exportedAt: -1 } },
      { $skip: (parseInt(page) - 1) * parseInt(pageSize) },
      { $limit: parseInt(pageSize) }
    ]);

    // 获取总数
    const totalCount = await CustomerData.aggregate([
      {
        $match: {
          merchantId,
          status: 'exported',
          exportedAt: { $exists: true }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d %H:%M',
              date: '$exportedAt'
            }
          }
        }
      },
      { $count: 'total' }
    ]);

    const total = totalCount[0]?.total || 0;

    res.json({
      success: true,
      data: history,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(total / parseInt(pageSize)),
      message: '获取导出历史成功'
    });
  } catch (error) {
    console.error('获取导出历史失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * 重新下载历史导出数据
 * POST /api/export/redownload
 */
router.post('/redownload', requireAuth, async (req, res) => {
  try {
    const merchantId = req.session.merchantId;
    const { exportTime } = req.body;

    if (!exportTime) {
      return res.status(400).json({
        success: false,
        message: '请提供导出时间'
      });
    }

    const CustomerData = require('../models/CustomerData');

    // 查找指定导出时间的数据
    const exportDate = new Date(exportTime);
    const startTime = new Date(exportDate.getTime() - 30000); // 前30秒
    const endTime = new Date(exportDate.getTime() + 30000);   // 后30秒

    const data = await CustomerData.find({
      merchantId,
      status: 'exported',
      exportedAt: {
        $gte: startTime,
        $lte: endTime
      }
    }).sort({ collectedAt: -1 }).lean();

    if (data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到对应的导出数据'
      });
    }

    // 使用导出服务重新生成Excel
    const ExcelJS = require('exceljs');
    const path = require('path');
    const fs = require('fs');

    // Excel表头定义
    const excelHeaders = [
      { key: 'date', header: '日期', width: 12 },
      { key: 'time', header: '时间', width: 12 },
      { key: 'messageSource', header: '私信来源', width: 15 },
      { key: 'customerService', header: '客服人员', width: 12 },
      { key: 'store', header: '门店', width: 20 },
      { key: 'customerType', header: '客人类型', width: 12 },
      { key: 'isValid', header: '是否有效', width: 10 },
      { key: 'firstMessage', header: '第一句话内容', width: 30 },
      { key: 'consultationType', header: '咨询类型', width: 15 },
      { key: 'consultationChannel', header: '咨询渠道', width: 15 },
      { key: 'platformNickname', header: '平台备注昵称', width: 20 },
      { key: 'contactInfo', header: '联系方式', width: 20 },
      { key: 'consultationLevel1', header: '咨询一级', width: 15 },
      { key: 'consultationLevel2', header: '咨询二级', width: 15 },
      { key: 'consultationLevel3', header: '咨询三级', width: 15 },
      { key: 'addWechat', header: '添加微信', width: 10 },
      { key: 'appointmentTime', header: '预约时间', width: 20 },
      { key: 'remarks', header: '备注', width: 30 },
      { key: 'contactCount', header: '留联数', width: 10 }
    ];

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('顾客信息');

    // 设置列定义
    worksheet.columns = excelHeaders;

    // 设置表头样式
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };

    // 格式化门店名称的辅助函数
    const formatStoreName = (storeName) => {
      if (!storeName) return '';

      // 匹配括号中的内容，支持中文括号（）和英文括号()
      const match = storeName.match(/[（(]([^）)]+)[）)]/);
      if (match && match[1]) {
        return match[1];
      }

      // 如果没有括号，返回原始名称
      return storeName;
    };

    // 添加数据行
    data.forEach(item => {
      worksheet.addRow({
        date: item.date || '',
        time: item.time || '',
        messageSource: item.messageSource || '',
        customerService: item.customerService || '',
        store: formatStoreName(item.store) || '',
        customerType: item.customerType || '',
        isValid: item.isValid || '',
        firstMessage: item.firstMessage || '',
        consultationType: item.consultationType || '',
        consultationChannel: item.consultationChannel || '',
        platformNickname: item.platformNickname || '',
        contactInfo: item.contactInfo || '',
        consultationLevel1: item.consultationLevel1 || '',
        consultationLevel2: item.consultationLevel2 || '',
        consultationLevel3: item.consultationLevel3 || '',
        addWechat: item.addWechat || '',
        appointmentTime: item.appointmentTime || '',
        remarks: item.remarks || '',
        contactCount: item.contactCount || ''
      });
    });

    // 生成文件名
    const timestamp = exportTime.replace(/[:.]/g, '-').replace(' ', '_');
    const filename = `历史导出_${timestamp}.xlsx`;
    const filepath = path.join(__dirname, '../../temp', filename);

    // 确保临时目录存在
    const tempDir = path.dirname(filepath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // 写入文件
    await workbook.xlsx.writeFile(filepath);

    // 设置响应头，触发文件下载
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`);

    // 发送文件
    res.sendFile(filepath, (err) => {
      if (err) {
        console.error('发送文件失败:', err);
        if (!res.headersSent) {
          res.status(500).json({
            success: false,
            message: '文件下载失败'
          });
        }
      } else {
        // 文件发送成功后，异步清理临时文件
        setTimeout(() => {
          try {
            if (fs.existsSync(filepath)) {
              fs.unlinkSync(filepath);
            }
          } catch (cleanupError) {
            console.error('清理临时文件失败:', cleanupError);
          }
        }, 5000);
      }
    });

  } catch (error) {
    console.error('重新下载历史数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
