const axios = require('axios');
const CustomerData = require('../models/CustomerData');

class DataService {
  constructor() {
    this.baseURL = 'https://m.dianping.com';
  }

  /**
   * 重新设计的大众点评数据提取流程（三步API调用）
   * @param {string} shopId - 店铺ID
   * @param {string} cookies - Cookie信息
   * @param {string} fromDate - 开始日期 (YYYY-MM-DD)
   * @param {string} toDate - 结束日期 (YYYY-MM-DD)
   * @returns {Promise<Object>} 提取结果
   */
  async extractCustomerDetails(shopId, cookies, fromDate, toDate) {
    try {
      const userSearchResult = await this.searchUsers(cookies, shopId, fromDate, toDate);
      if (!userSearchResult.success) {
        return userSearchResult;
      }

      const customers = userSearchResult.data;
      const results = [];
      const errors = [];

      for (let i = 0; i < customers.length; i++) {
        const customer = customers[i];

        try {
          const basicInfo = await this.getCustomerDetailInfo(cookies, customer.imUserId, customer.fromUid);
          await this.delay(500);
          const messageInfo = await this.getFirstMessageAndCustomerService(cookies, customer.imUserId, customer.fromUid);
          
          // 数据整合和字段映射
          const customerDetail = {
            // 精确字段映射
            date: this.extractDateFromTimestamp(basicInfo.firstCommunication),
            time: this.extractTimeFromTimestamp(basicInfo.firstCommunication),
            messageSource: basicInfo.source || '',
            customerService: messageInfo.customerService || '',
            store: this.transformStoreName(basicInfo.shopName || ''),
            customerType: '',
            isValid: '',
            firstMessage: messageInfo.firstMessage || '',
            consultationType: '私信',
            consultationChannel: '点评',
            platformNickname: this.extractNicknameFromClientName(basicInfo.clientName) || '',
            contactInfo: basicInfo.phone || '',
            consultationLevel1: '',
            consultationLevel2: '',
            consultationLevel3: '',
            addWechat: basicInfo.weChat || '',
            appointmentTime: '',
            remarks: basicInfo.note || '',
            contactCount: this.calculateContactCountString(basicInfo.weChat, basicInfo.phone)
          };
          
          results.push(customerDetail);
          
          // 添加延迟避免请求过快，确保串行执行
          await this.delay(1000);
          
        } catch (error) {
          errors.push({
            userId: customer.imUserId,
            error: error.message
          });
        }
      }
      
      // 不保存到数据库，直接返回数据

      return {
        success: true,
        data: results,
        errors: errors,
        total: customers.length,
        successCount: results.length,
        errorCount: errors.length,
        message: `成功提取 ${results.length}/${customers.length} 个顾客信息`
      };
      
    } catch (error) {
      return {
        success: false,
        message: `数据提取失败: ${error.message}`
      };
    }
  }

  /**
   * 获取店铺列表
   * 调用接口：https://m.dianping.com/merchant/im/account/pageShopList
   * @param {string} cookies - Cookie信息
   * @returns {Promise<Object>} 店铺列表结果
   */
  async getShopList(cookies) {
    try {
      const headers = this.buildStandardHeaders(cookies);

      const apiUrl = `${this.baseURL}/merchant/im/account/pageShopList`;
      const params = {
        cityId: 0,
        pageNum: 1,
        pageSize: 50,
        yodaReady: 'h5',
        csecplatform: '4',
        csecversion: '3.2.1',
        mtgsig: this.generateMtgsig()
      };

      const response = await axios.get(apiUrl, {
        headers,
        params,
        timeout: 30000
      });

      if (response.data && response.data.code === 200) {
        const shopInfoList = response.data.data?.shopInfoList || [];

        // 格式化店铺信息
        const shops = shopInfoList.map(shop => ({
          shopId: shop.shopId,
          shopName: shop.shopName,
          branchName: shop.branchName,
          fullName: shop.branchName ? `${shop.shopName}-${shop.branchName}` : shop.shopName,
          cityId: shop.cityId,
          shopType: shop.shopType,
          rawData: shop
        }));

        return {
          success: true,
          data: shops,
          total: shops.length,
          message: `获取到 ${shops.length} 个店铺`
        };
      } else {
        return {
          success: false,
          message: '获取店铺列表失败，API返回异常'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: `获取店铺列表失败: ${error.message}`
      };
    }
  }

  /**
   * 第一步：搜索用户列表（支持分页）
   * 调用接口：https://m.dianping.com/merchant/im/user/search
   * @param {string} cookies - Cookie信息
   * @param {string} shopId - 店铺ID
   * @param {string} fromDate - 开始日期 (YYYY-MM-DD)
   * @param {string} toDate - 结束日期 (YYYY-MM-DD)
   * @returns {Promise<Object>} 用户搜索结果
   */
  async searchUsers(cookies, shopId, fromDate, toDate) {
    try {
      const headers = this.buildStandardHeaders(cookies);
      const apiUrl = `${this.baseURL}/merchant/im/user/search`;

      let allCustomers = [];
      let pageNum = 1;
      const pageSize = 10; // 增加每页数量以减少请求次数
      let totalCount = 0;
      let totalPages = 0;

      console.log(`开始搜索用户列表，店铺ID: ${shopId}, 时间范围: ${fromDate} - ${toDate}`);

      // 第一次请求获取总数
      const firstParams = {
        pageNum: 1,
        pageSize: pageSize,
        shopId: shopId,
        fromFirstContact: fromDate,
        toFirstContact: toDate,
        yodaReady: 'h5',
        csecplatform: '4',
        csecversion: '3.2.0',
        mtgsig: this.generateMtgsig()
      };

      const firstResponse = await axios.get(apiUrl, {
        headers,
        params: firstParams,
        timeout: 30000
      });

      if (!firstResponse.data || firstResponse.data.code !== 200) {
        return {
          success: false,
          message: '搜索用户失败，API返回异常'
        };
      }

      // 获取总数和总页数
      const firstData = firstResponse.data.data || {};
      totalCount = firstData.totalCount || 0;
      totalPages = Math.ceil(totalCount / pageSize);

      console.log(`找到 ${totalCount} 个用户，共 ${totalPages} 页`);

      if (totalCount === 0) {
        return {
          success: true,
          data: [],
          total: 0,
          message: '该时间段内没有找到用户记录'
        };
      }

      // 处理第一页数据
      const firstRecords = firstData.records || [];
      if (firstRecords.length > 0) {
        const firstCustomers = firstRecords.map(record => ({
          fromUid: record.shopId,
          imUserId: record.userId,
          clientName: record.clientName,
          phone: record.phone,
          firstContact: record.firstContact,
          lastContact: record.lastContact,
          note: record.note,
          lastContactorName: record.lastContactorName,
          rawData: record
        })).filter(customer => customer.fromUid && customer.imUserId);

        allCustomers.push(...firstCustomers);
        console.log(`第1页获取到 ${firstCustomers.length} 个用户`);
      }

      // 如果有多页，继续请求剩余页面
      for (pageNum = 2; pageNum <= totalPages; pageNum++) {
        console.log(`正在请求第 ${pageNum}/${totalPages} 页...`);

        const params = {
          pageNum: pageNum,
          pageSize: pageSize,
          shopId: shopId,
          fromFirstContact: fromDate,
          toFirstContact: toDate,
          yodaReady: 'h5',
          csecplatform: '4',
          csecversion: '3.2.0',
          mtgsig: this.generateMtgsig()
        };

        try {
          const response = await axios.get(apiUrl, {
            headers,
            params,
            timeout: 30000
          });

          if (response.data && response.data.code === 200) {
            const records = response.data.data?.records || [];

            if (records.length > 0) {
              const customers = records.map(record => ({
                fromUid: record.shopId,
                imUserId: record.userId,
                clientName: record.clientName,
                phone: record.phone,
                firstContact: record.firstContact,
                lastContact: record.lastContact,
                note: record.note,
                lastContactorName: record.lastContactorName,
                rawData: record
              })).filter(customer => customer.fromUid && customer.imUserId);

              allCustomers.push(...customers);
              console.log(`第${pageNum}页获取到 ${customers.length} 个用户`);
            }
          } else {
            console.warn(`第${pageNum}页请求失败，跳过`);
          }
        } catch (error) {
          console.error(`第${pageNum}页请求出错:`, error.message);
          // 继续处理下一页，不中断整个流程
        }

        // 添加延迟避免请求过快
        await this.delay(800);
      }

      console.log(`用户搜索完成，总共获取到 ${allCustomers.length}/${totalCount} 个用户`);

      return {
        success: true,
        data: allCustomers,
        total: allCustomers.length,
        totalCount: totalCount,
        message: `获取到 ${allCustomers.length}/${totalCount} 个用户记录`
      };

    } catch (error) {
      console.error('搜索用户失败:', error);
      return {
        success: false,
        message: `搜索用户失败: ${error.message}`
      };
    }
  }

  /**
   * 第二步：获取顾客详细信息
   * 调用接口：https://m.dianping.com/merchant/im/user/{imUserId}/detail
   * @param {string} cookies - Cookie信息
   * @param {string} imUserId - 用户ID
   * @param {string} fromUid - 店铺ID
   * @returns {Promise<Object>} 顾客详细信息
   */
  async getCustomerDetailInfo(cookies, imUserId, fromUid) {
    try {
      const headers = this.buildStandardHeaders(cookies);
      
      const apiUrl = `${this.baseURL}/merchant/im/user/${imUserId}/detail`;
      const params = {
        shopId: fromUid,
        source: '100900',
        yodaReady: 'h5',
        csecplatform: '4',
        csecversion: '3.2.0',
        mtgsig: this.generateMtgsig()
      };

      const response = await this.retryApiCall(async () => {
        return await axios.get(apiUrl, {
          headers,
          params,
          timeout: 15000
        });
      }, 3, 1000);

      if (response.data && response.data.code === 200) {
        const data = response.data.data || response.data.msg;

        let result = {
          firstCommunication: data.firstCommunication,
          source: data.source || '',
          clientName: data.clientName || data.nickname || '',
          weChat: data.weChat || data.wechat || '',
          note: data.note || data.remark || '',
          shopName: data.shopName || '',
          phone: data.phone || data.mobile || ''
        };

        // 检查phone是否是脱敏格式（包含****），如果是则调用queryphone接口获取完整电话
        if (result.phone && result.phone.includes('****')) {
          try {
            const fullPhone = await this.getFullPhoneNumber(cookies, imUserId, fromUid);
            if (fullPhone) {
              result.phone = fullPhone;
            }
          } catch (error) {
            // 保持原有的脱敏电话号码
          }
        }

        return result;
      } else {
        throw new Error('获取顾客详情失败，API返回异常');
      }
      
    } catch (error) {
      throw error;
    }
  }

  /**
   * 获取完整电话号码
   * 调用接口：https://m.dianping.com/merchant/im/user/{imUserId}/queryphone
   * @param {string} cookies - Cookie信息
   * @param {string} imUserId - 用户ID
   * @param {string} fromUid - 店铺ID
   * @returns {Promise<string>} 完整电话号码
   */
  async getFullPhoneNumber(cookies, imUserId, fromUid) {
    try {
      const headers = this.buildStandardHeaders(cookies);

      const apiUrl = `${this.baseURL}/merchant/im/user/${imUserId}/queryphone`;
      const params = {
        shopId: fromUid,
        yodaReady: 'h5',
        csecplatform: '4',
        csecversion: '3.2.0',
        mtgsig: this.generateMtgsig()
      };

      const response = await this.retryApiCall(async () => {
        return await axios.get(apiUrl, {
          headers,
          params,
          timeout: 15000
        });
      }, 3, 1000);

      if (response.data && response.data.code === 200) {
        const data = response.data.data || response.data.msg;
        const fullPhone = data?.telPhone || data?.phone || '';

        if (fullPhone) {
          return fullPhone;
        } else {
          return '';
        }
      } else {
        return '';
      }

    } catch (error) {
      return '';
    }
  }

  /**
   * 第三步：获取第一句话和客服人员信息
   * 调用接口：https://m.dianping.com/general/platform/dzim/json/shop/contacts
   * @param {string} cookies - Cookie信息
   * @param {string} imUserId - 用户ID
   * @param {string} fromUid - 店铺ID
   * @returns {Promise<Object>} 包含第一句话内容和客服人员信息的对象
   */
  async getFirstMessageAndCustomerService(cookies, imUserId, fromUid) {
    try {
      const headers = this.buildStandardHeaders(cookies);
      const contactsUrl = `${this.baseURL}/general/platform/dzim/json/shop/contacts`;

      let firstMessage = '';
      let currentMessageId = null;
      let callCount = 0;
      const maxCalls = 10;
      let lastValidFirstMessage = ''; // 保存最后一次有数据请求中的第一条消息
      let customerService = ''; // 保存客服人员信息

      while (callCount < maxCalls) {
        callCount++;

        const callParams = {
          shopId: fromUid,
          userId: imUserId,
          clientType: '100900',
          yodaReady: 'h5',
          csecplatform: '4',
          csecversion: '3.2.0',
          mtgsig: this.generateMtgsig()
        };

        // 第一次请求不带lastMessageId，之后用第一个消息的messageId
        if (currentMessageId) {
          callParams.lastMessageId = currentMessageId;
        }

        const response = await this.retryApiCall(async () => {
          return await axios.get(contactsUrl, {
            headers,
            params: callParams,
            timeout: 15000
          });
        }, 3, 800);

        if (!response.data) {
          break;
        }

        const messageInfoList = response.data.messageInfoDoList;

        // 如果返回空数组，说明已经到了最后，使用上一次有数据请求中的第一条消息
        if (!messageInfoList || messageInfoList.length === 0) {
          if (lastValidFirstMessage) {
            firstMessage = lastValidFirstMessage;
          }
          break;
        }

        // 保存这次请求中第一条消息，这可能是最终的第一句话
        const firstMessageInList = messageInfoList[0];
        if (firstMessageInList) {
          // 如果unitTitle是"商品信息"，则使用imSendUnit.title作为第一句话
          if (firstMessageInList.unitTitle === '商品信息' &&
              firstMessageInList.imSendUnit &&
              firstMessageInList.imSendUnit.title) {
            lastValidFirstMessage = firstMessageInList.imSendUnit.title;
          }
          // 如果unitTitle是"交易订单信息"，则从content字段中解析title
          else if (firstMessageInList.unitTitle === '交易订单信息' &&
                   firstMessageInList.content) {
            try {
              const contentObj = JSON.parse(firstMessageInList.content);
              if (contentObj.title) {
                lastValidFirstMessage = contentObj.title;
              } else if (firstMessageInList.message) {
                lastValidFirstMessage = firstMessageInList.message;
              }
            } catch (error) {
              // 如果JSON解析失败，使用普通的message字段
              if (firstMessageInList.message) {
                lastValidFirstMessage = firstMessageInList.message;
              }
            }
          }
          // 如果unitTitle是"提醒信息"，则从content字段中解析tipsContent
          else if (firstMessageInList.unitTitle === '提醒信息' &&
                   firstMessageInList.content) {
            try {
              const contentObj = JSON.parse(firstMessageInList.content);
              if (contentObj.tipsContent) {
                lastValidFirstMessage = contentObj.tipsContent;
              } else if (firstMessageInList.message) {
                lastValidFirstMessage = firstMessageInList.message;
              }
            } catch (error) {
              // 如果JSON解析失败，使用普通的message字段
              if (firstMessageInList.message) {
                lastValidFirstMessage = firstMessageInList.message;
              }
            }
          }
          else if (firstMessageInList.message) {
            // 否则使用普通的message字段
            lastValidFirstMessage = firstMessageInList.message;
          }
        }

        // 查找客服人员：遍历整个消息数组，找到任何一个有shopAccountName的消息
        if (!customerService) {
          for (const message of messageInfoList) {
            if (message.shopAccountName && message.shopAccountName.trim() !== '') {
              customerService = this.mapCustomerServiceName(message.shopAccountName);
              break; // 找到第一个就停止
            }
          }
        }

        // 取第一个消息的messageId作为下次请求的lastMessageId
        currentMessageId = firstMessageInList.messageId;

        await this.delay(800);
      }

      return {
        firstMessage: firstMessage,
        customerService: customerService
      };

    } catch (error) {
      return { firstMessage: '', customerService: '' };
    }
  }

  /**
   * 构建标准请求头（与您提供的可用请求完全一致）
   * @param {string} cookies - Cookie信息
   * @returns {Object} 请求头
   */
  buildStandardHeaders(cookies) {
    return {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Cookie': cookies,
      'Origin': 'https://g.dianping.com',
      'Pragma': 'no-cache',
      'Referer': 'https://g.dianping.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-site',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"'
    };
  }

  /**
   * 生成固定的mtgsig参数（使用您提供的可用值）
   * @returns {string} 固定的mtgsig值
   */
  generateMtgsig() {
    // 使用您提供的可用mtgsig参数
    return '%7B%22a1%22%3A%221.2%22%2C%22a2%22%3A1752833525160%2C%22a3%22%3A%221752810409185UISGOES75613c134b6a252faa6802015be905513227%22%2C%22a5%22%3A%22z6uxh9kd63f24LV1X%2Bn8cB0VkrL%2BXgyguGVphCV4wN56oIf0cWq%3D%22%2C%22a6%22%3A%22hs1.61Cs4ta%2FVgQuOok0OlbCiD2wdEtX55mNTXB%2FamsgS%2FvHT07368HgLOoEgBfCy7O%2FAefLTJ%2FRk%2B3LzYkEjAQ96JsNjwj1KL7KL8KhU%2BipAvqylSXRb59vzqogORqypJyJQ%22%2C%22a8%22%3A%2224ca384442d784a97dfbdc78eadf6565%22%2C%22a9%22%3A%223.2.0%2C7%2C146%22%2C%22a10%22%3A%22c9%22%2C%22x0%22%3A4%2C%22d1%22%3A%22c60acdc49e5935839303427b5853f85a%22%7D';
  }

  /**
   * 从客户姓名中提取昵称（取"-"后面的部分）
   * @param {string} clientName - 客户姓名
   * @returns {string} 提取的昵称
   */
  extractNicknameFromClientName(clientName) {
    if (!clientName || typeof clientName !== 'string') {
      return '';
    }

    // 查找"-"的位置
    const dashIndex = clientName.indexOf('-');

    // 如果找到"-"，返回"-"后面的部分
    if (dashIndex !== -1 && dashIndex < clientName.length - 1) {
      return clientName.substring(dashIndex + 1).trim();
    }

    // 如果没有找到"-"，返回原始名称
    return clientName.trim();
  }

  /**
   * 映射客服人员姓名
   * @param {string} shopAccountName - 客服账号名
   * @returns {string} 客服人员姓名
   */
  mapCustomerServiceName(shopAccountName) {
    if (!shopAccountName) return '';

    const serviceMapping = {
      'JS_cuicui': '黄翠翠',
      'TRUU_hy': '黄滢',
      'TRUU_hxx': '郝新星',
      'TRUU_ZG': '主管'
    };

    return serviceMapping[shopAccountName] || '主管';
  }

  /**
   * 转换门店名称
   * @param {string} shopName - 原始门店名称
   * @returns {string} 转换后的门店名称
   */
  transformStoreName(shopName) {
    if (!shopName) return '';

    // 门店名称转换规则
    if (shopName.includes('今识医疗美容(日月光店)')) {
      return '日月光店';
    }
    if (shopName.includes('今识医疗美容（陆家嘴店）')) {
      return '陆家嘴店';
    }

    // 如果没有匹配的规则，返回原始名称
    return shopName;
  }

  /**
   * 计算留联数
   * @param {string} weChatValue - 微信字段值
   * @param {string} phoneValue - 电话字段值
   * @returns {string} "0" 或 "1"
   */
  calculateContactCountString(weChatValue, phoneValue) {
    const hasWeChat = weChatValue && String(weChatValue).trim() !== '';
    const hasPhone = phoneValue && String(phoneValue).trim() !== '';

    if (hasWeChat || hasPhone) {
      return '1';
    } else {
      return '0';
    }
  }

  /**
   * 从时间戳提取日期部分
   * @param {string|number} timestamp - 时间戳或日期字符串
   * @returns {string} 日期字符串 (YYYY-MM-DD格式)
   */
  extractDateFromTimestamp(timestamp) {
    try {
      if (!timestamp) return '';

      let date;
      if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      } else {
        return '';
      }

      if (isNaN(date.getTime())) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    } catch (error) {
      return '';
    }
  }

  /**
   * 从时间戳提取时间部分
   * @param {string|number} timestamp - 时间戳或日期字符串
   * @returns {string} 时间字符串 (HH:MM:SS格式)
   */
  extractTimeFromTimestamp(timestamp) {
    try {
      if (!timestamp) return '';

      let date;
      if (typeof timestamp === 'number') {
        date = new Date(timestamp);
      } else if (typeof timestamp === 'string') {
        date = new Date(timestamp);
      } else {
        return '';
      }

      if (isNaN(date.getTime())) return '';

      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${hours}:${minutes}:${seconds}`;
    } catch (error) {
      return '';
    }
  }

  /**
   * 验证顾客数据完整性
   * @param {Object} basicInfo - 基本信息
   * @param {string} firstMessage - 第一句话
   * @returns {boolean} 是否有效
   */
  validateCustomerData(basicInfo, firstMessage) {
    return !!(basicInfo.clientName && (basicInfo.weChat || basicInfo.phone || firstMessage));
  }

  /**
   * 保存提取的数据到数据库
   * @param {Array} extractedData - 提取的数据
   * @returns {Promise<void>}
   */
  async saveExtractedData(extractedData) {
    try {
      const savePromises = extractedData.map(async (customerData) => {
        const existingRecord = await CustomerData.findOne({
          userId: customerData.userId,
          shopId: customerData.shopId,
          merchantId: customerData.merchantId
        });

        if (existingRecord) {
          Object.assign(existingRecord, customerData);
          existingRecord.updatedAt = new Date();
          return existingRecord.save();
        } else {
          const newRecord = new CustomerData(customerData);
          return newRecord.save();
        }
      });

      await Promise.all(savePromises);
      console.log(`成功保存 ${extractedData.length} 条顾客数据到数据库`);
    } catch (error) {
      console.error('保存提取数据失败:', error.message);
    }
  }

  /**
   * 获取简化的统计信息
   * @param {string} merchantId - 商户ID
   * @returns {Promise<Object>} 统计结果
   */
  async getSimpleStats(merchantId) {
    try {
      const stats = await CustomerData.aggregate([
        { $match: { merchantId } },
        {
          $group: {
            _id: null,
            totalRecords: { $sum: 1 },
            validRecords: {
              $sum: {
                $cond: [{ $eq: ['$isValid', '是'] }, 1, 0]
              }
            },
            wechatAdded: {
              $sum: {
                $cond: [{ $eq: ['$addWechat', '是'] }, 1, 0]
              }
            }
          }
        }
      ]);

      const result = stats[0] || {
        totalRecords: 0,
        validRecords: 0,
        wechatAdded: 0
      };

      return {
        success: true,
        stats: result,
        message: '获取统计信息成功'
      };
    } catch (error) {
      console.error('获取统计信息失败:', error.message);
      return {
        success: false,
        message: '获取统计信息失败'
      };
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 带重试的API调用
   * @param {Function} apiCall - API调用函数
   * @param {number} maxRetries - 最大重试次数
   * @param {number} baseDelay - 基础延迟时间（毫秒）
   * @returns {Promise} API调用结果
   */
  async retryApiCall(apiCall, maxRetries = 3, baseDelay = 1000) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        console.log(`API调用失败，第 ${attempt}/${maxRetries} 次尝试:`, error.message);

        if (attempt === maxRetries) {
          throw error;
        }

        // 指数退避：每次重试延迟时间翻倍
        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`等待 ${delay}ms 后重试...`);
        // await this.delay(delay);
      }
    }
  }

  /**
   * 获取历史数据
   * @param {string} merchantId - 商户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 历史数据结果
   */
  async getHistoryData(merchantId, options = {}) {
    try {
      const { page = 1, pageSize = 50, startDate, endDate, status } = options;

      // 构建查询条件
      const query = { merchantId };

      // 日期范围筛选
      if (startDate || endDate) {
        query.collectedAt = {};
        if (startDate) {
          query.collectedAt.$gte = new Date(startDate);
        }
        if (endDate) {
          query.collectedAt.$lte = new Date(endDate + 'T23:59:59.999Z');
        }
      }

      // 状态筛选
      if (status) {
        query.status = status;
      }

      // 获取总数
      const total = await CustomerData.countDocuments(query);

      // 获取分页数据
      const data = await CustomerData.find(query)
        .sort({ collectedAt: -1 })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .lean();

      return {
        success: true,
        data: data,
        total: total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('获取历史数据失败:', error);
      return {
        success: false,
        message: '获取历史数据失败',
        error: error.message
      };
    }
  }
}

module.exports = new DataService();
