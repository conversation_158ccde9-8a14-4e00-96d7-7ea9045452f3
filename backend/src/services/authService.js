const axios = require('axios');
const Merchant = require('../models/Merchant');
const CookieConfig = require('../models/CookieConfig');

class AuthService {
  constructor() {
    this.baseURL = 'https://g.dianping.com';
    this.mobileURL = 'https://m.dianping.com';
  }

  // ==================== 多Cookie管理功能 ====================

  /**
   * 获取所有可用的Cookie配置
   * @param {string} merchantId - 商户ID（可选，如果不提供则返回所有）
   * @returns {Promise<Object>} Cookie配置列表
   */
  async getCookieConfigs(merchantId = null) {
    try {
      const query = { isActive: true };
      if (merchantId) {
        query.merchantId = merchantId;
      }

      const configs = await CookieConfig.find(query)
        .select('name description merchantId merchantName lastUsed useCount isValid lastValidated')
        .sort({ lastUsed: -1 });

      return {
        success: true,
        data: configs,
        total: configs.length,
        message: `获取到 ${configs.length} 个Cookie配置`
      };
    } catch (error) {
      console.error('获取Cookie配置失败:', error);
      return {
        success: false,
        message: '获取Cookie配置失败'
      };
    }
  }

  /**
   * 创建新的Cookie配置
   * @param {Object} configData - Cookie配置数据
   * @returns {Promise<Object>} 创建结果
   */
  async createCookieConfig(configData) {
    try {
      const { name, description, cookies, password, merchantId } = configData;

      // 验证必需字段
      if (!name || !cookies || !password || !merchantId) {
        return {
          success: false,
          message: '请提供完整的配置信息'
        };
      }

      // 从Cookie中提取商户信息
      const extractedMerchantId = this.extractMerchantId(cookies);
      if (!extractedMerchantId) {
        return {
          success: false,
          message: '无法从Cookie中提取商户ID，请检查Cookie信息是否完整'
        };
      }

      // 验证Cookie有效性
      const validationResult = await this.validateMerchantAuth(cookies, extractedMerchantId);
      if (!validationResult.success) {
        return {
          success: false,
          message: `Cookie验证失败: ${validationResult.message}`
        };
      }

      // 创建Cookie配置
      const cookieConfig = new CookieConfig({
        name: name.trim(),
        description: description?.trim() || '',
        merchantId: extractedMerchantId,
        merchantName: validationResult.merchantInfo?.name || `商户${extractedMerchantId}`
      });

      // 设置密码和Cookie
      await cookieConfig.setPassword(password);
      cookieConfig.setCookies(cookies);

      // 设置验证状态
      await cookieConfig.updateValidation(true);

      await cookieConfig.save();

      return {
        success: true,
        data: {
          id: cookieConfig._id,
          name: cookieConfig.name,
          merchantId: cookieConfig.merchantId,
          merchantName: cookieConfig.merchantName
        },
        message: 'Cookie配置创建成功'
      };

    } catch (error) {
      console.error('创建Cookie配置失败:', error);

      if (error.code === 'DUPLICATE_NAME') {
        return {
          success: false,
          message: '配置名称已存在，请使用其他名称'
        };
      }

      return {
        success: false,
        message: '创建Cookie配置失败'
      };
    }
  }

  /**
   * 使用密码进行认证（自动匹配配置）
   * @param {string} password - 密码
   * @returns {Promise<Object>} 认证结果
   */
  async authenticateWithPassword(password) {
    try {
      // 获取所有活跃的Cookie配置
      const configs = await CookieConfig.find({ isActive: true }).sort({ lastUsed: -1 });

      if (configs.length === 0) {
        return {
          success: false,
          message: '没有可用的配置'
        };
      }

      // 遍历所有配置，尝试用密码进行认证
      for (const config of configs) {
        try {
          // 验证密码
          const isPasswordValid = await config.validatePassword(password);
          if (!isPasswordValid) {
            continue; // 密码不匹配，尝试下一个配置
          }

          // 密码匹配，尝试认证
          const authResult = await this.authenticateWithConfig(config._id.toString(), password);
          if (authResult.success) {
            return authResult; // 认证成功，返回结果
          }
        } catch (error) {
          // 当前配置认证失败，继续尝试下一个
          console.log(`配置 ${config.name} 认证失败:`, error.message);
          continue;
        }
      }

      // 所有配置都尝试失败
      return {
        success: false,
        message: '密码错误或没有匹配的配置'
      };

    } catch (error) {
      console.error('密码认证失败:', error);
      return {
        success: false,
        message: '认证过程中发生错误'
      };
    }
  }

  /**
   * 使用Cookie配置进行认证
   * @param {string} configId - Cookie配置ID
   * @param {string} password - 密码
   * @returns {Promise<Object>} 认证结果
   */
  async authenticateWithConfig(configId, password) {
    try {
      // 获取Cookie配置
      const cookieConfig = await CookieConfig.findById(configId);
      if (!cookieConfig || !cookieConfig.isActive) {
        return {
          success: false,
          message: 'Cookie配置不存在或已禁用'
        };
      }

      // 验证密码
      const isPasswordValid = await cookieConfig.validatePassword(password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 获取解密的Cookie
      let cookies;
      try {
        cookies = cookieConfig.getCookies();
      } catch (error) {
        return {
          success: false,
          message: 'Cookie解密失败，请联系管理员'
        };
      }

      // 验证Cookie是否仍然有效
      const validationResult = await this.validateMerchantAuth(cookies, cookieConfig.merchantId);
      if (!validationResult.success) {
        // 更新验证状态
        await cookieConfig.updateValidation(false, validationResult.message);

        return {
          success: false,
          message: `Cookie已失效: ${validationResult.message}`
        };
      }

      // 更新使用统计和验证状态
      await cookieConfig.updateUsage();
      await cookieConfig.updateValidation(true);

      // 获取或创建商户记录
      let merchant = await Merchant.findOne({ merchantId: cookieConfig.merchantId });
      if (!merchant) {
        merchant = new Merchant({
          merchantId: cookieConfig.merchantId,
          merchantName: cookieConfig.merchantName,
          stores: validationResult.merchantInfo?.stores || []
        });
      }

      // 设置当前使用的Cookie配置
      await merchant.setCurrentCookieConfig(cookieConfig._id);

      return {
        success: true,
        data: {
          merchantId: cookieConfig.merchantId,
          merchantName: cookieConfig.merchantName,
          cookies: cookies,
          configId: cookieConfig._id,
          configName: cookieConfig.name
        },
        message: '认证成功'
      };

    } catch (error) {
      console.error('Cookie配置认证失败:', error);
      return {
        success: false,
        message: '认证过程中发生错误'
      };
    }
  }

  /**
   * 更新Cookie配置
   * @param {string} configId - Cookie配置ID
   * @param {Object} updateData - 更新数据
   * @param {string} password - 当前密码
   * @returns {Promise<Object>} 更新结果
   */
  async updateCookieConfig(configId, updateData, password) {
    try {
      const cookieConfig = await CookieConfig.findById(configId);
      if (!cookieConfig || !cookieConfig.isActive) {
        return {
          success: false,
          message: 'Cookie配置不存在或已禁用'
        };
      }

      // 验证密码
      const isPasswordValid = await cookieConfig.validatePassword(password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 更新基本信息
      if (updateData.name) {
        cookieConfig.name = updateData.name.trim();
      }
      if (updateData.description !== undefined) {
        cookieConfig.description = updateData.description.trim();
      }

      // 更新Cookie（如果提供）
      if (updateData.cookies) {
        const extractedMerchantId = this.extractMerchantId(updateData.cookies);
        if (extractedMerchantId !== cookieConfig.merchantId) {
          return {
            success: false,
            message: '新Cookie的商户ID与原配置不匹配'
          };
        }

        // 验证新Cookie
        const validationResult = await this.validateMerchantAuth(updateData.cookies, extractedMerchantId);
        if (!validationResult.success) {
          return {
            success: false,
            message: `新Cookie验证失败: ${validationResult.message}`
          };
        }

        cookieConfig.setCookies(updateData.cookies);
        await cookieConfig.updateValidation(true);
      }

      // 更新密码（如果提供）
      if (updateData.newPassword) {
        await cookieConfig.setPassword(updateData.newPassword);
      }

      await cookieConfig.save();

      return {
        success: true,
        data: {
          id: cookieConfig._id,
          name: cookieConfig.name,
          description: cookieConfig.description
        },
        message: 'Cookie配置更新成功'
      };

    } catch (error) {
      console.error('更新Cookie配置失败:', error);

      if (error.code === 'DUPLICATE_NAME') {
        return {
          success: false,
          message: '配置名称已存在，请使用其他名称'
        };
      }

      return {
        success: false,
        message: '更新Cookie配置失败'
      };
    }
  }

  /**
   * 删除Cookie配置
   * @param {string} configId - Cookie配置ID
   * @param {string} password - 密码
   * @returns {Promise<Object>} 删除结果
   */
  async deleteCookieConfig(configId, password) {
    try {
      const cookieConfig = await CookieConfig.findById(configId);
      if (!cookieConfig) {
        return {
          success: false,
          message: 'Cookie配置不存在'
        };
      }

      // 验证密码
      const isPasswordValid = await cookieConfig.validatePassword(password);
      if (!isPasswordValid) {
        return {
          success: false,
          message: '密码错误'
        };
      }

      // 检查是否有商户正在使用此配置
      const merchant = await Merchant.findOne({ currentCookieConfigId: configId });
      if (merchant) {
        // 软删除：标记为不活跃
        cookieConfig.isActive = false;
        await cookieConfig.save();

        // 清除商户的当前配置
        merchant.currentCookieConfigId = null;
        await merchant.save();
      } else {
        // 硬删除
        await CookieConfig.findByIdAndDelete(configId);
      }

      return {
        success: true,
        message: 'Cookie配置删除成功'
      };

    } catch (error) {
      console.error('删除Cookie配置失败:', error);
      return {
        success: false,
        message: '删除Cookie配置失败'
      };
    }
  }

  /**
   * 验证Cookie配置的有效性
   * @param {string} configId - Cookie配置ID
   * @returns {Promise<Object>} 验证结果
   */
  async validateCookieConfig(configId) {
    try {
      const cookieConfig = await CookieConfig.findById(configId);
      if (!cookieConfig || !cookieConfig.isActive) {
        return {
          success: false,
          message: 'Cookie配置不存在或已禁用'
        };
      }

      const cookies = cookieConfig.getCookies();
      const validationResult = await this.validateMerchantAuth(cookies, cookieConfig.merchantId);

      // 更新验证状态
      await cookieConfig.updateValidation(validationResult.success,
        validationResult.success ? null : validationResult.message);

      return {
        success: true,
        data: {
          configId: cookieConfig._id,
          name: cookieConfig.name,
          isValid: validationResult.success,
          lastValidated: cookieConfig.lastValidated,
          validationError: cookieConfig.validationError
        },
        message: validationResult.success ? 'Cookie配置有效' : 'Cookie配置已失效'
      };

    } catch (error) {
      console.error('验证Cookie配置失败:', error);
      return {
        success: false,
        message: '验证Cookie配置失败'
      };
    }
  }

  // ==================== 兼容性方法 ====================

  /**
   * 验证商户认证信息（保持原有接口兼容性）
   * @param {string} cookies - 商户平台cookies
   * @param {string} merchantId - 商户ID
   * @returns {Promise<Object>} 验证结果
   */
  async validateMerchantAuth(cookies, merchantId) {
    try {
      // 构建请求头（基于真实大众点评请求）
      const headers = {
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Cookie': cookies,
        'Origin': 'https://g.dianping.com',
        'Pragma': 'no-cache',
        'Referer': 'https://g.dianping.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"'
      };

      // 解析Cookie获取商户信息
      const cookieObj = this.parseCookies(cookies);
      const shopId = cookieObj.mpmerchant_portal_shopid || merchantId;

      console.log('验证商户认证，商户ID:', shopId);

      // 使用您提供的验证接口
      const validateUrl = `${this.mobileURL}/dzim/merchantentry/accountpermission/load`;
      const validateParams = {
        clienttype: '100900',
        yodaReady: 'h5',
        csecplatform: '4',
        csecversion: '3.2.0',
        // 使用固定的mtgsig参数
        mtgsig: '%7B%22a1%22%3A%221.2%22%2C%22a2%22%3A1752818283399%2C%22a3%22%3A%221752810409185UISGOES75613c134b6a252faa6802015be905513227%22%2C%22a5%22%3A%22NMZtR1vhi%2BN9sUCpVwrx%2B9t%2BT%2Fh6tAG7B56Vejz5T9Yr%2B%2FD6Kc%3D%3D%22%2C%22a6%22%3A%22hs1.6H4F4eH26KM%2F5%2BtXp%2F%2FV5DM%2BIZ5vVJf8QO59GTpAHFzBez%2BD4Al421hLUnO9pNVsZ0iaQ12EOfgqp23kCvsO2RQI0L242S1ea8u3rjyNPS1rxT9hCWvFRjnnBPD1ZqDBw%22%2C%22a8%22%3A%22dfec5df5d26c9f7b2d78a0378d4bf4b5%22%2C%22a9%22%3A%223.2.0%2C7%2C195%22%2C%22a10%22%3A%226c%22%2C%22x0%22%3A4%2C%22d1%22%3A%229913ad1af24a2466d123596f6b59897f%22%7D'
      };

      const response = await axios.get(validateUrl, {
        headers,
        params: validateParams,
        timeout: 10000
      });

      console.log('认证验证响应:', response.data);

      // 检查响应是否表示认证成功
      if (response.data && response.data.code === 200 && response.data.msg && response.data.msg.hasPermission) {
        return {
          success: true,
          merchantInfo: {
            merchantId: shopId,
            shopId: shopId,
            cookies: cookies,
            name: `商户${shopId}`,
            stores: [{
              storeId: shopId,
              storeName: `门店${shopId}`,
              isActive: true
            }]
          },
          message: '认证验证成功'
        };
      } else {
        return {
          success: false,
          message: response.data?.msg?.tipMsg || '认证信息无效或已过期'
        };
      }
    } catch (error) {
      console.error('商户认证验证失败:', error.message);

      // 如果是401或403错误，说明认证失败
      if (error.response && [401, 403].includes(error.response.status)) {
        return {
          success: false,
          message: '认证信息无效或已过期'
        };
      }

      // 其他错误可能是网络问题，但Cookie可能是有效的
      return {
        success: true,
        merchantInfo: {
          merchantId: merchantId,
          name: `商户${merchantId}`,
          stores: []
        },
        message: '认证信息已保存（网络验证失败）'
      };
    }
  }

  /**
   * 从Cookie中提取商户ID
   * @param {string} cookieString - Cookie字符串
   * @returns {string|null} 商户ID
   */
  extractMerchantId(cookieString) {
    const match = cookieString.match(/mpmerchant_portal_shopid=([^;]+)/);
    return match ? match[1] : null;
  }

  /**
   * 解析Cookie字符串
   * @param {string} cookieString - Cookie字符串
   * @returns {Object} Cookie对象
   */
  parseCookies(cookieString) {
    const cookies = {};
    if (cookieString) {
      cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
          cookies[name] = value;
        }
      });
    }
    return cookies;
  }

  /**
   * 获取最近使用的Cookie配置
   * @param {number} limit - 限制数量
   * @returns {Promise<Object>} 最近使用的配置列表
   */
  async getRecentlyUsedConfigs(limit = 5) {
    try {
      const configs = await CookieConfig.getRecentlyUsed(limit);

      return {
        success: true,
        data: configs.map(config => ({
          id: config._id,
          name: config.name,
          merchantId: config.merchantId,
          merchantName: config.merchantName,
          lastUsed: config.lastUsed,
          useCount: config.useCount,
          isValid: config.isValid
        })),
        message: `获取到 ${configs.length} 个最近使用的配置`
      };
    } catch (error) {
      console.error('获取最近使用配置失败:', error);
      return {
        success: false,
        message: '获取最近使用配置失败'
      };
    }
  }

  /**
   * 批量验证Cookie配置
   * @param {Array} configIds - Cookie配置ID数组
   * @returns {Promise<Object>} 批量验证结果
   */
  async batchValidateCookieConfigs(configIds) {
    try {
      const results = [];

      for (const configId of configIds) {
        const result = await this.validateCookieConfig(configId);
        results.push({
          configId,
          ...result
        });
      }

      const validCount = results.filter(r => r.success && r.data?.isValid).length;
      const invalidCount = results.length - validCount;

      return {
        success: true,
        data: results,
        summary: {
          total: results.length,
          valid: validCount,
          invalid: invalidCount
        },
        message: `批量验证完成：${validCount} 个有效，${invalidCount} 个无效`
      };

    } catch (error) {
      console.error('批量验证Cookie配置失败:', error);
      return {
        success: false,
        message: '批量验证Cookie配置失败'
      };
    }
  }

  /**
   * 自动选择最佳Cookie配置
   * @param {string} merchantId - 商户ID（可选）
   * @returns {Promise<Object>} 最佳配置
   */
  async getOptimalCookieConfig(merchantId = null) {
    try {
      const query = { isActive: true, isValid: true };
      if (merchantId) {
        query.merchantId = merchantId;
      }

      // 优先选择最近使用且有效的配置
      const config = await CookieConfig.findOne(query)
        .sort({ lastUsed: -1, useCount: -1 });

      if (!config) {
        return {
          success: false,
          message: '没有找到可用的Cookie配置'
        };
      }

      return {
        success: true,
        data: {
          id: config._id,
          name: config.name,
          merchantId: config.merchantId,
          merchantName: config.merchantName,
          lastUsed: config.lastUsed,
          useCount: config.useCount
        },
        message: '找到最佳Cookie配置'
      };

    } catch (error) {
      console.error('获取最佳Cookie配置失败:', error);
      return {
        success: false,
        message: '获取最佳Cookie配置失败'
      };
    }
  }

  /**
   * 保存认证信息到数据库（单一商户模式）
   * @param {Object} merchantData - 商户数据
   * @returns {Promise<Object>} 保存结果
   */
  async saveMerchantAuth(merchantData) {
    try {
      const { merchantId, merchantName, cookies } = merchantData;

      // 查找现有的唯一商户记录，如果没有则创建
      let merchant = await Merchant.findOne();

      if (merchant) {
        // 更新现有商户信息
        merchant.merchantId = merchantId;
        merchant.merchantName = merchantName || `商户${merchantId}`;
        merchant.cookies = cookies;
        merchant.lastLoginTime = new Date();
        merchant.isActive = true;
      } else {
        // 创建新商户（系统中的唯一商户）
        merchant = new Merchant({
          merchantId,
          merchantName: merchantName || `商户${merchantId}`,
          cookies,
          stores: [{
            storeId: merchantId,
            storeName: `门店${merchantId}`,
            isActive: true
          }],
          lastLoginTime: new Date(),
          isActive: true
        });
      }

      await merchant.save();

      return {
        success: true,
        merchant: {
          merchantId: merchant.merchantId,
          merchantName: merchant.merchantName,
          cookies: merchant.cookies,
          stores: merchant.stores,
          lastLoginTime: merchant.lastLoginTime
        },
        message: '认证信息保存成功'
      };
    } catch (error) {
      console.error('保存商户认证信息失败:', error.message);
      return {
        success: false,
        message: '保存认证信息失败'
      };
    }
  }

  /**
   * 获取商户列表
   * @returns {Promise<Array>} 商户列表
   */
  async getMerchantList() {
    try {
      const merchants = await Merchant.find({ isActive: true })
        .select('merchantId merchantName stores lastLoginTime')
        .sort({ lastLoginTime: -1 });

      return {
        success: true,
        merchants,
        message: '获取商户列表成功'
      };
    } catch (error) {
      console.error('获取商户列表失败:', error.message);
      return {
        success: false,
        merchants: [],
        message: '获取商户列表失败'
      };
    }
  }

  /**
   * 获取商户认证信息
   * @param {string} merchantId - 商户ID
   * @returns {Promise<Object>} 商户信息
   */
  async getMerchantAuth(merchantId) {
    try {
      const merchant = await Merchant.findOne({ merchantId, isActive: true });

      if (!merchant) {
        return {
          success: false,
          message: '商户不存在或已禁用'
        };
      }

      return {
        success: true,
        merchant: {
          merchantId: merchant.merchantId,
          merchantName: merchant.merchantName,
          cookies: merchant.cookies,
          authToken: merchant.authToken,
          stores: merchant.stores,
          lastLoginTime: merchant.lastLoginTime
        },
        message: '获取商户信息成功'
      };
    } catch (error) {
      console.error('获取商户认证信息失败:', error.message);
      return {
        success: false,
        message: '获取商户信息失败'
      };
    }
  }

  /**
   * 自动验证已保存的商户认证信息（单一商户模式）
   * @returns {Promise<Object>} 验证结果
   */
  async autoValidateAuth() {
    try {
      // 获取系统中的唯一商户
      const merchant = await Merchant.findOne({ isActive: true });

      if (!merchant) {
        return {
          success: false,
          message: '没有找到已保存的认证信息'
        };
      }

      // 验证Cookie是否仍然有效
      const validateResult = await this.validateMerchantAuth(
        merchant.cookies,
        merchant.merchantId
      );

      if (validateResult.success) {
        // 更新最后登录时间
        merchant.lastLoginTime = new Date();
        await merchant.save();

        return {
          success: true,
          merchant: {
            merchantId: merchant.merchantId,
            merchantName: merchant.merchantName,
            cookies: merchant.cookies,
            stores: merchant.stores,
            lastLoginTime: merchant.lastLoginTime
          },
          message: '自动验证成功'
        };
      } else {
        return {
          success: false,
          message: '已保存的认证信息已失效'
        };
      }
    } catch (error) {
      console.error('自动验证失败:', error.message);
      return {
        success: false,
        message: '自动验证失败'
      };
    }
  }
}

module.exports = new AuthService();
