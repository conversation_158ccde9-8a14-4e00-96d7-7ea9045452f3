const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const rateLimit = require('express-rate-limit');
const { ipKeyGenerator } = require('express-rate-limit');

class SecurityService {
  constructor() {
    // 加密配置
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.saltRounds = 12;
    
    // 获取加密密钥
    this.encryptionKey = this.getEncryptionKey();
  }

  /**
   * 获取或生成加密密钥
   * @returns {Buffer} 加密密钥
   */
  getEncryptionKey() {
    const keyString = process.env.COOKIE_ENCRYPTION_KEY || 'default-encryption-key-change-in-production';
    return crypto.scryptSync(keyString, 'salt', this.keyLength);
  }

  /**
   * 加密数据
   * @param {string} plaintext - 明文数据
   * @returns {Object} 加密结果 {encrypted, iv, tag}
   */
  encrypt(plaintext) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv(this.algorithm, this.encryptionKey, iv);
      cipher.setAutoPadding(true);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const tag = cipher.getAuthTag();

      return {
        encrypted: encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      };
    } catch (error) {
      console.error('数据加密失败:', error);
      throw new Error('数据加密失败');
    }
  }

  /**
   * 解密数据
   * @param {string} encrypted - 加密数据
   * @param {string} iv - 初始化向量
   * @param {string} tag - 认证标签
   * @returns {string} 明文数据
   */
  decrypt(encrypted, iv, tag) {
    try {
      const ivBuffer = Buffer.from(iv, 'hex');
      const decipher = crypto.createDecipheriv(this.algorithm, this.encryptionKey, ivBuffer);
      decipher.setAutoPadding(true);
      decipher.setAuthTag(Buffer.from(tag, 'hex'));

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('数据解密失败:', error);
      throw new Error('数据解密失败');
    }
  }

  /**
   * 简化的加密方法（向后兼容）
   * @param {string} plaintext - 明文数据
   * @returns {Object} 加密结果 {encrypted, iv}
   */
  encryptSimple(plaintext) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, iv);
      cipher.setAutoPadding(true);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return {
        encrypted: encrypted,
        iv: iv.toString('hex')
      };
    } catch (error) {
      console.error('简单加密失败:', error);
      throw new Error('简单加密失败');
    }
  }

  /**
   * 简化的解密方法（向后兼容）
   * @param {string} encrypted - 加密数据
   * @param {string} iv - 初始化向量
   * @returns {string} 明文数据
   */
  decryptSimple(encrypted, iv) {
    try {
      const ivBuffer = Buffer.from(iv, 'hex');
      const decipher = crypto.createDecipheriv('aes-256-cbc', this.encryptionKey, ivBuffer);
      decipher.setAutoPadding(true);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      console.error('简单解密失败:', error);
      throw new Error('简单解密失败');
    }
  }

  /**
   * 哈希密码
   * @param {string} password - 明文密码
   * @returns {Promise<string>} 哈希后的密码
   */
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, this.saltRounds);
    } catch (error) {
      console.error('密码哈希失败:', error);
      throw new Error('密码哈希失败');
    }
  }

  /**
   * 验证密码
   * @param {string} password - 明文密码
   * @param {string} hash - 哈希密码
   * @returns {Promise<boolean>} 密码是否匹配
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('密码验证失败:', error);
      return false;
    }
  }

  /**
   * 生成安全的随机密码
   * @param {number} length - 密码长度
   * @param {Object} options - 选项
   * @returns {string} 随机密码
   */
  generateSecurePassword(length = 12, options = {}) {
    const {
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = true,
      includeSymbols = false,
      excludeSimilar = true
    } = options;

    let charset = '';
    if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (includeNumbers) charset += '0123456789';
    if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

    if (excludeSimilar) {
      charset = charset.replace(/[0O1lI]/g, '');
    }

    if (charset.length === 0) {
      throw new Error('密码字符集不能为空');
    }

    let password = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }

    return password;
  }

  /**
   * 验证密码强度
   * @param {string} password - 密码
   * @returns {Object} 验证结果
   */
  validatePasswordStrength(password) {
    const result = {
      isValid: false,
      score: 0,
      feedback: [],
      requirements: {
        minLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumbers: false,
        hasSymbols: false
      }
    };

    if (!password) {
      result.feedback.push('密码不能为空');
      return result;
    }

    // 检查最小长度
    if (password.length >= 8) {
      result.requirements.minLength = true;
      result.score += 1;
    } else {
      result.feedback.push('密码长度至少8位');
    }

    // 检查大写字母
    if (/[A-Z]/.test(password)) {
      result.requirements.hasUppercase = true;
      result.score += 1;
    } else {
      result.feedback.push('密码应包含大写字母');
    }

    // 检查小写字母
    if (/[a-z]/.test(password)) {
      result.requirements.hasLowercase = true;
      result.score += 1;
    } else {
      result.feedback.push('密码应包含小写字母');
    }

    // 检查数字
    if (/[0-9]/.test(password)) {
      result.requirements.hasNumbers = true;
      result.score += 1;
    } else {
      result.feedback.push('密码应包含数字');
    }

    // 检查特殊字符
    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
      result.requirements.hasSymbols = true;
      result.score += 1;
    } else {
      result.feedback.push('密码应包含特殊字符');
    }

    // 检查常见弱密码
    const commonPasswords = ['123456', 'password', '123456789', '12345678', '12345', '1234567', 'admin', 'qwerty'];
    if (commonPasswords.includes(password.toLowerCase())) {
      result.feedback.push('密码过于简单，请使用更复杂的密码');
      result.score = Math.max(0, result.score - 2);
    }

    // 计算最终结果
    result.isValid = result.score >= 3; // 至少满足3个要求
    
    if (result.score === 5) {
      result.strength = '很强';
    } else if (result.score >= 4) {
      result.strength = '强';
    } else if (result.score >= 3) {
      result.strength = '中等';
    } else if (result.score >= 2) {
      result.strength = '弱';
    } else {
      result.strength = '很弱';
    }

    return result;
  }

  /**
   * 创建登录限制中间件
   * @param {Object} options - 限制选项
   * @returns {Function} Express中间件
   */
  createLoginRateLimit(options = {}) {
    const {
      windowMs = 15 * 60 * 1000, // 15分钟
      max = 5, // 最多5次尝试
      message = '登录尝试次数过多，请稍后再试',
      skipSuccessfulRequests = true
    } = options;

    return rateLimit({
      windowMs,
      max,
      message: {
        success: false,
        message
      },
      skipSuccessfulRequests,
      keyGenerator: (req) => {
        // 使用 ipKeyGenerator 正确处理 IPv6 地址
        const ipKey = ipKeyGenerator(req);
        const userKey = req.body.configId || req.body.merchantId || 'unknown';
        return `login_${ipKey}_${userKey}`;
      }
    });
  }

  /**
   * 创建API限制中间件
   * @param {Object} options - 限制选项
   * @returns {Function} Express中间件
   */
  createApiRateLimit(options = {}) {
    const {
      windowMs = 60 * 1000, // 1分钟
      max = 100, // 最多100次请求
      message = 'API请求过于频繁，请稍后再试'
    } = options;

    return rateLimit({
      windowMs,
      max,
      message: {
        success: false,
        message
      }
    });
  }

  /**
   * 生成安全的会话ID
   * @returns {string} 会话ID
   */
  generateSessionId() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 生成CSRF令牌
   * @returns {string} CSRF令牌
   */
  generateCSRFToken() {
    return crypto.randomBytes(32).toString('base64');
  }
}

module.exports = new SecurityService();
