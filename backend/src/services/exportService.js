const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const CustomerData = require('../models/CustomerData');
const dataService = require('./dataService');

class ExportService {
  constructor() {
    // Excel表头定义
    this.excelHeaders = [
      { key: 'date', header: '日期', width: 12 },
      { key: 'time', header: '时间', width: 12 },
      { key: 'messageSource', header: '私信来源', width: 15 },
      { key: 'customerService', header: '客服人员', width: 12 },
      { key: 'store', header: '门店', width: 20 },
      { key: 'customerType', header: '客人类型', width: 12 },
      { key: 'isValid', header: '是否有效', width: 10 },
      { key: 'firstMessage', header: '第一句话内容', width: 30 },
      { key: 'consultationType', header: '咨询类型', width: 15 },
      { key: 'consultationChannel', header: '咨询渠道', width: 15 },
      { key: 'platformNickname', header: '平台备注昵称', width: 20 },
      { key: 'contactInfo', header: '联系方式', width: 20 },
      { key: 'consultationLevel1', header: '咨询一级', width: 15 },
      { key: 'consultationLevel2', header: '咨询二级', width: 15 },
      { key: 'consultationLevel3', header: '咨询三级', width: 15 },
      { key: 'addWechat', header: '添加微信', width: 10 },
      { key: 'appointmentTime', header: '预约时间', width: 20 },
      { key: 'remarks', header: '备注', width: 30 },
      { key: 'contactCount', header: '留联数', width: 10 }
    ];
  }

  /**
   * 直接导出Excel文件（使用提取的数据，不保存数据库）
   * @param {string} merchantId - 商户ID
   * @param {string} cookies - Cookie信息
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 导出结果
   */
  async exportToExcel(merchantId, cookies, filters = {}) {
    try {
      console.log('开始提取数据并直接生成Excel...');

      // 执行数据提取流程
      const extractResult = await dataService.extractCustomerDetails(merchantId, cookies);
      if (!extractResult.success) {
        return {
          success: false,
          message: `数据提取失败: ${extractResult.message}`
        };
      }

      console.log(`数据提取完成，成功: ${extractResult.successCount}，失败: ${extractResult.errorCount}`);

      // 直接使用提取的数据，不从数据库读取
      const data = extractResult.data;
      const merchantName = '大众点评商户'; // 可以从session或其他地方获取

      // 创建工作簿
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('顾客信息');

      // 设置列定义
      worksheet.columns = this.excelHeaders;

      // 设置表头样式
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      };
      headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

      // 添加数据行
      data.forEach((item, index) => {
        const row = worksheet.addRow(this.formatRowData(item));
        
        // 设置行样式
        if (index % 2 === 1) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F2F2F2' }
          };
        }
      });

      // 自动调整列宽
      worksheet.columns.forEach(column => {
        if (column.width < 8) column.width = 8;
        if (column.width > 50) column.width = 50;
      });

      // 添加边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
      const filename = `顾客信息_${merchantName || merchantId}_${timestamp}.xlsx`;
      const filepath = path.join(__dirname, '../../temp', filename);

      // 确保临时目录存在
      const tempDir = path.dirname(filepath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // 写入文件
      await workbook.xlsx.writeFile(filepath);

      // 更新数据状态为已导出
      await this.updateExportStatus(merchantId, filters);

      return {
        success: true,
        filename,
        filepath,
        recordCount: data.length,
        message: 'Excel文件生成成功'
      };
    } catch (error) {
      console.error('导出Excel失败:', error.message);
      return {
        success: false,
        message: `导出失败: ${error.message}`
      };
    }
  }

  /**
   * 获取导出数据
   * @param {string} merchantId - 商户ID
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 数据结果
   */
  async getExportData(merchantId, filters = {}) {
    try {
      const query = { merchantId };
      
      // 构建查询条件
      if (filters.storeId) {
        query.storeId = filters.storeId;
      }
      
      if (filters.startDate || filters.endDate) {
        query.collectedAt = {};
        if (filters.startDate) {
          query.collectedAt.$gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          query.collectedAt.$lte = new Date(filters.endDate + 'T23:59:59.999Z');
        }
      }

      // 状态筛选
      if (filters.status) {
        query.status = filters.status;
      }

      // 获取数据
      const data = await CustomerData.find(query)
        .sort({ collectedAt: -1 })
        .lean();

      // 获取商户名称
      const Merchant = require('../models/Merchant');
      const merchant = await Merchant.findOne({ merchantId }).lean();
      const merchantName = merchant ? merchant.merchantName : '';

      return {
        success: true,
        data,
        merchantName,
        message: '获取导出数据成功'
      };
    } catch (error) {
      console.error('获取导出数据失败:', error.message);
      return {
        success: false,
        message: '获取导出数据失败'
      };
    }
  }

  /**
   * 格式化行数据
   * @param {Object} item - 数据项
   * @returns {Object} 格式化后的行数据
   */
  formatRowData(item) {
    return {
      date: item.date || '',
      time: item.time || '',
      messageSource: item.messageSource || '',
      customerService: item.customerService || '',
      store: this.formatStoreName(item.store) || '',
      customerType: item.customerType || '',
      isValid: item.isValid || '',
      firstMessage: item.firstMessage || '',
      consultationType: item.consultationType || '',
      consultationChannel: item.consultationChannel || '',
      platformNickname: item.platformNickname || '',
      contactInfo: item.contactInfo || '',
      consultationLevel1: item.consultationLevel1 || '',
      consultationLevel2: item.consultationLevel2 || '',
      consultationLevel3: item.consultationLevel3 || '',
      addWechat: item.addWechat || '',
      appointmentTime: item.appointmentTime || '',
      remarks: item.remarks || '',
      contactCount: item.contactCount || ''
    };
  }

  /**
   * 格式化门店名称，提取括号中的内容
   * @param {string} storeName - 原始门店名称
   * @returns {string} 格式化后的门店名称
   */
  formatStoreName(storeName) {
    if (!storeName) return '';

    // 匹配括号中的内容，支持中文括号（）和英文括号()
    const match = storeName.match(/[（(]([^）)]+)[）)]/);
    if (match && match[1]) {
      return match[1];
    }

    // 如果没有括号，返回原始名称
    return storeName;
  }

  /**
   * 更新导出状态
   * @param {string} merchantId - 商户ID
   * @param {Object} filters - 筛选条件
   * @returns {Promise<void>}
   */
  async updateExportStatus(merchantId, filters) {
    try {
      const query = { merchantId, status: { $ne: 'exported' } };
      
      if (filters.storeId) {
        query.storeId = filters.storeId;
      }
      
      if (filters.startDate || filters.endDate) {
        query.collectedAt = {};
        if (filters.startDate) {
          query.collectedAt.$gte = new Date(filters.startDate);
        }
        if (filters.endDate) {
          query.collectedAt.$lte = new Date(filters.endDate + 'T23:59:59.999Z');
        }
      }

      await CustomerData.updateMany(query, { 
        status: 'exported',
        exportedAt: new Date()
      });
    } catch (error) {
      console.error('更新导出状态失败:', error.message);
    }
  }

  /**
   * 清理临时文件
   * @param {string} filepath - 文件路径
   * @returns {Promise<void>}
   */
  async cleanupTempFile(filepath) {
    try {
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error.message);
    }
  }

  /**
   * 获取导出统计信息
   * @param {string} merchantId - 商户ID
   * @returns {Promise<Object>} 统计信息
   */
  async getExportStats(merchantId) {
    try {
      const stats = await CustomerData.aggregate([
        { $match: { merchantId } },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        total: 0,
        pending: 0,
        processed: 0,
        exported: 0
      };

      stats.forEach(stat => {
        result[stat._id] = stat.count;
        result.total += stat.count;
      });

      return {
        success: true,
        stats: result,
        message: '获取统计信息成功'
      };
    } catch (error) {
      console.error('获取导出统计失败:', error.message);
      return {
        success: false,
        message: '获取统计信息失败'
      };
    }
  }
}

module.exports = new ExportService();
