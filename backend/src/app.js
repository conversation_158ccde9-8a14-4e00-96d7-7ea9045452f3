const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

const connectDB = require('./config/database');
const authRoutes = require('./routes/auth');
const dataRoutes = require('./routes/data');
const exportRoutes = require('./routes/export');

const app = express();
const PORT = process.env.PORT || 3000;

// 连接数据库
connectDB();

// 安全中间件 - 禁用CSP以允许所有内容
app.use(helmet({
  contentSecurityPolicy: false, // 完全禁用CSP
  crossOriginEmbedderPolicy: false
}));

// 跨域配置 - 允许所有请求
app.use(cors({
  origin: true, // 允许所有来源
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['Set-Cookie']
}));

// 请求限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 基础中间件
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// 会话配置
app.use(session({
  secret: process.env.SESSION_SECRET || 'default-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // 暂时禁用secure以支持HTTPS代理
    httpOnly: true,
    sameSite: 'lax', // 允许跨站点请求携带cookie
    maxAge: 24 * 60 * 60 * 1000 // 24小时
  }
}));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/data', dataRoutes);
app.use('/api/export', exportRoutes);

// 静态文件服务（前端打包文件）
const frontendDistPath = path.join(__dirname, '../dist');
app.use(express.static(frontendDistPath));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// SPA路由处理 - 所有非API请求都返回index.html
app.get('*', (req, res, next) => {
  // 跳过API路由和健康检查
  if (req.path.startsWith('/api') || req.path === '/health') {
    return next();
  }

  // 返回前端应用的index.html
  res.sendFile(path.join(frontendDistPath, 'index.html'), (err) => {
    if (err) {
      console.error('发送index.html失败:', err);
      res.status(500).json({
        success: false,
        message: '前端应用加载失败'
      });
    }
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? '服务器内部错误' : err.message
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

const server = app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`环境: ${process.env.NODE_ENV}`);
});

// 设置服务器超时时间为10分钟
server.timeout = 600000;
server.keepAliveTimeout = 600000;
server.headersTimeout = 610000;

module.exports = app;
