const mongoose = require('mongoose');

const customerDataSchema = new mongoose.Schema({
  // 关联商户
  merchantId: {
    type: String,
    required: true,
    index: true
  },
  storeId: {
    type: String,
    index: true
  },
  // 数据字段（对应Excel表头）
  date: String,                    // 日期
  time: String,                    // 时间
  messageSource: String,           // 私信来源
  customerService: String,         // 客服人员
  store: String,                   // 门店
  customerType: String,            // 客人类型
  isValid: String,                 // 是否有效
  firstMessage: String,            // 第一句话内容
  consultationType: String,        // 咨询类型
  consultationChannel: String,     // 咨询渠道
  platformNickname: String,        // 平台备注昵称
  contactInfo: String,             // 联系方式
  consultationLevel1: String,      // 咨询一级
  consultationLevel2: String,      // 咨询二级
  consultationLevel3: String,      // 咨询三级
  addWechat: String,              // 添加微信
  appointmentTime: String,         // 预约时间
  remarks: String,                 // 备注
  contactCount: String,            // 留联数

  // 新增字段用于API数据映射
  userId: String,                  // 用户ID
  shopId: String,                  // 店铺ID
  
  // 原始数据（用于调试和数据恢复）
  rawData: {
    type: mongoose.Schema.Types.Mixed
  },
  
  // 数据状态
  status: {
    type: String,
    enum: ['pending', 'processed', 'exported'],
    default: 'pending'
  },
  
  // 数据来源
  dataSource: {
    type: String,
    default: 'dianping_api'
  },
  
  // 采集时间
  collectedAt: {
    type: Date,
    default: Date.now
  },

  // 导出时间
  exportedAt: {
    type: Date,
    index: true
  }
}, {
  timestamps: true
});

// 复合索引
customerDataSchema.index({ merchantId: 1, collectedAt: -1 });
customerDataSchema.index({ merchantId: 1, shopId: 1, collectedAt: -1 });
customerDataSchema.index({ userId: 1, shopId: 1, merchantId: 1 }, { unique: true });
customerDataSchema.index({ status: 1 });
customerDataSchema.index({ merchantId: 1, exportedAt: -1 });
customerDataSchema.index({ merchantId: 1, status: 1, exportedAt: -1 });

module.exports = mongoose.model('CustomerData', customerDataSchema);
