const mongoose = require('mongoose');

const merchantSchema = new mongoose.Schema({
  // 商户基本信息
  merchantId: {
    type: String,
    required: true,
    unique: true
  },
  merchantName: {
    type: String,
    required: true
  },

  // 兼容性字段（保留用于向后兼容）
  cookies: {
    type: String,
    // 不再必需，因为现在使用CookieConfig模型
  },
  authToken: {
    type: String
  },

  // 店铺信息
  stores: [{
    storeId: String,
    storeName: String,
    isActive: {
      type: Boolean,
      default: true
    }
  }],

  // 状态信息
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginTime: {
    type: Date,
    default: Date.now
  },

  // 当前使用的Cookie配置ID（新增）
  currentCookieConfigId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'CookieConfig'
  },

  // 配置信息
  settings: {
    autoExport: {
      type: Boolean,
      default: false
    },
    exportFormat: {
      type: String,
      enum: ['xlsx', 'csv'],
      default: 'xlsx'
    },

  }
}, {
  timestamps: true
});

// 索引
merchantSchema.index({ merchantId: 1 });
merchantSchema.index({ isActive: 1 });
merchantSchema.index({ currentCookieConfigId: 1 });



// 实例方法

/**
 * 获取当前使用的Cookie配置
 * @returns {Promise<Object>} Cookie配置对象
 */
merchantSchema.methods.getCurrentCookieConfig = async function() {
  if (!this.currentCookieConfigId) {
    return null;
  }

  const CookieConfig = mongoose.model('CookieConfig');
  return await CookieConfig.findById(this.currentCookieConfigId);
};

/**
 * 设置当前使用的Cookie配置
 * @param {string} cookieConfigId - Cookie配置ID
 * @returns {Promise<Object>} 更新后的商户对象
 */
merchantSchema.methods.setCurrentCookieConfig = async function(cookieConfigId) {
  this.currentCookieConfigId = cookieConfigId;
  this.lastLoginTime = new Date();
  return await this.save();
};

/**
 * 获取所有可用的Cookie配置
 * @returns {Promise<Array>} Cookie配置列表
 */
merchantSchema.methods.getAvailableCookieConfigs = async function() {
  const CookieConfig = mongoose.model('CookieConfig');
  return await CookieConfig.find({
    merchantId: this.merchantId,
    isActive: true
  }).sort({ lastUsed: -1 });
};

/**
 * 检查是否有可用的Cookie配置
 * @returns {Promise<boolean>} 是否有可用配置
 */
merchantSchema.methods.hasAvailableCookieConfigs = async function() {
  const configs = await this.getAvailableCookieConfigs();
  return configs.length > 0;
};

// 静态方法





module.exports = mongoose.model('Merchant', merchantSchema);
