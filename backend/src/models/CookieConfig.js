const mongoose = require('mongoose');
const securityService = require('../services/securityService');

const cookieConfigSchema = new mongoose.Schema({
  // 基本信息
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  
  // 商户信息
  merchantId: {
    type: String,
    required: true,
    index: true
  },
  merchantName: {
    type: String,
    required: true
  },
  
  // 认证信息（加密存储）
  encryptedCookies: {
    type: String,
    required: true
  },
  cookiesIV: {
    type: String,
    required: true
  },
  
  // 密码保护（哈希存储）
  passwordHash: {
    type: String,
    required: true
  },
  
  // 状态信息
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  // 使用统计
  lastUsed: {
    type: Date,
    index: true
  },
  useCount: {
    type: Number,
    default: 0
  },
  
  // 验证状态
  lastValidated: {
    type: Date
  },
  isValid: {
    type: Boolean,
    default: true
  },
  validationError: {
    type: String
  },
  
  // 配置选项
  settings: {
    autoValidate: {
      type: Boolean,
      default: true
    },
    notifyOnExpiry: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// 索引
cookieConfigSchema.index({ merchantId: 1, isActive: 1 });
cookieConfigSchema.index({ name: 1, merchantId: 1 }, { unique: true });
cookieConfigSchema.index({ lastUsed: -1 });

// 虚拟字段
cookieConfigSchema.virtual('isExpired').get(function() {
  if (!this.lastValidated) return false;
  const daysSinceValidation = (Date.now() - this.lastValidated.getTime()) / (1000 * 60 * 60 * 24);
  return daysSinceValidation > 7; // 7天未验证视为可能过期
});

// 实例方法

/**
 * 设置密码（加密存储）
 * @param {string} password - 明文密码
 */
cookieConfigSchema.methods.setPassword = async function(password) {
  // 验证密码强度
  const strengthCheck = securityService.validatePasswordStrength(password);
  if (!strengthCheck.isValid) {
    const error = new Error(`密码强度不足: ${strengthCheck.feedback.join(', ')}`);
    error.code = 'WEAK_PASSWORD';
    error.details = strengthCheck;
    throw error;
  }

  this.passwordHash = await securityService.hashPassword(password);
};

/**
 * 验证密码
 * @param {string} password - 明文密码
 * @returns {boolean} 密码是否正确
 */
cookieConfigSchema.methods.validatePassword = async function(password) {
  return await securityService.verifyPassword(password, this.passwordHash);
};

/**
 * 加密并设置Cookie
 * @param {string} cookies - 明文Cookie
 */
cookieConfigSchema.methods.setCookies = function(cookies) {
  try {
    const encryptResult = securityService.encryptSimple(cookies);
    this.encryptedCookies = encryptResult.encrypted;
    this.cookiesIV = encryptResult.iv;
  } catch (error) {
    console.error('Cookie加密失败:', error);
    throw new Error('Cookie加密失败');
  }
};

/**
 * 解密获取Cookie
 * @returns {string} 明文Cookie
 */
cookieConfigSchema.methods.getCookies = function() {
  try {
    return securityService.decryptSimple(this.encryptedCookies, this.cookiesIV);
  } catch (error) {
    console.error('Cookie解密失败:', error);
    throw new Error('Cookie解密失败');
  }
};

/**
 * 更新使用统计
 */
cookieConfigSchema.methods.updateUsage = function() {
  this.lastUsed = new Date();
  this.useCount += 1;
  return this.save();
};

/**
 * 更新验证状态
 * @param {boolean} isValid - 是否有效
 * @param {string} error - 错误信息（可选）
 */
cookieConfigSchema.methods.updateValidation = function(isValid, error = null) {
  this.lastValidated = new Date();
  this.isValid = isValid;
  this.validationError = error;
  return this.save();
};

// 静态方法

/**
 * 根据商户ID获取活跃的Cookie配置
 * @param {string} merchantId - 商户ID
 * @returns {Array} Cookie配置列表
 */
cookieConfigSchema.statics.getActiveConfigs = function(merchantId = null) {
  const query = { isActive: true };
  if (merchantId) {
    query.merchantId = merchantId;
  }
  return this.find(query).sort({ lastUsed: -1 });
};

/**
 * 根据名称和商户ID查找配置
 * @param {string} name - 配置名称
 * @param {string} merchantId - 商户ID（可选）
 * @returns {Object} Cookie配置
 */
cookieConfigSchema.statics.findByName = function(name, merchantId = null) {
  const query = { name, isActive: true };
  if (merchantId) {
    query.merchantId = merchantId;
  }
  return this.findOne(query);
};

/**
 * 获取最近使用的配置
 * @param {number} limit - 限制数量
 * @returns {Array} Cookie配置列表
 */
cookieConfigSchema.statics.getRecentlyUsed = function(limit = 5) {
  return this.find({ isActive: true, lastUsed: { $exists: true } })
    .sort({ lastUsed: -1 })
    .limit(limit);
};

// 中间件

// 保存前的验证
cookieConfigSchema.pre('save', function(next) {
  // 确保名称唯一性（在同一商户下）
  if (this.isNew || this.isModified('name')) {
    this.constructor.findOne({
      name: this.name,
      merchantId: this.merchantId,
      _id: { $ne: this._id },
      isActive: true
    }).then(existing => {
      if (existing) {
        const error = new Error('该名称已存在');
        error.code = 'DUPLICATE_NAME';
        return next(error);
      }
      next();
    }).catch(next);
  } else {
    next();
  }
});

// 删除时的清理
cookieConfigSchema.pre('remove', function(next) {
  // 这里可以添加删除时的清理逻辑
  console.log(`删除Cookie配置: ${this.name}`);
  next();
});

module.exports = mongoose.model('CookieConfig', cookieConfigSchema);
