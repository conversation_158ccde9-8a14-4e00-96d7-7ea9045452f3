import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  optimizeDeps: {
    include: ['xlsx-js-style', 'element-plus', 'vue', 'vue-router', 'pinia'],
    force: true
  },
  build: {
    // 打包输出到后端的dist文件夹
    outDir: path.resolve(__dirname, '../backend/dist'),
    // 清空输出目录
    emptyOutDir: true,
    // 生成source map用于调试
    sourcemap: false,
    // 压缩配置
    minify: 'esbuild',
    // 目标浏览器
    target: 'es2015',
    // 避免某些依赖的问题
    commonjsOptions: {
      include: [/node_modules/]
    },
    rollupOptions: {
      external: [],
      output: {
        // 禁用代码分割，将所有代码打包到一个文件中
        manualChunks: undefined,
        // 确保单一入口文件
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    }
  },
  // 开发服务器配置
  server: {
    port: 8010,
    host: true,
    // 代理API请求到后端
    proxy: {
      '/api': {
        target: 'http://localhost:8011',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
