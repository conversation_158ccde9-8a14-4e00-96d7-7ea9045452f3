{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "preview": "vite preview", "deploy": "npm run build:prod && cd ../backend && npm install --production"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.4"}}