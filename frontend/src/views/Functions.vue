<template>
  <div class="functions-page">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
          </svg>
        </div>
        <div class="logo-text">
          <h1>数据提取</h1>
        </div>
      </div>

      <nav class="sidebar-nav">
        <div class="nav-section">
          <h3 class="nav-title">数据管理</h3>
          <div class="nav-item active">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
            <span>数据提取</span>
          </div>
          <!-- <div class="nav-item" @click="handleViewHistory">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
            <span>历史记录</span>
          </div> -->
        </div>


      </nav>

      <div class="sidebar-footer">
        <button @click="handleLogout" class="logout-btn">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
            <polyline points="16,17 21,12 16,7"/>
            <line x1="21" y1="12" x2="9" y2="12"/>
          </svg>
          <span>退出登录</span>
        </button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 顶部状态栏 -->
      <header class="content-header">
        <div class="header-left">
          <h2 class="page-title">数据提取工具</h2>
          <p class="page-subtitle">配置参数并提取大众点评数据</p>
        </div>
        <div class="header-right">
          <div class="status-indicator">
            <div class="status-dot"></div>
            <span>系统正常</span>
          </div>
        </div>
      </header>

      <!-- 配置面板 -->
      <section class="config-panel">
        <div class="panel-header">
          <h3>提取配置</h3>
          <p>请选择店铺和时间范围</p>
        </div>

        <div class="config-form">
          <!-- 店铺选择区域 -->
          <div class="config-section">
            <div class="section-title">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
              </svg>
              <span>店铺选择</span>
              <div class="selection-count">{{ selectedShopIds.length }}/{{ shopOptions.length }}</div>
            </div>
            <el-select
              v-model="selectedShopIds"
              multiple
              placeholder="正在加载店铺列表..."
              :loading="loadingShops"
              class="shop-selector"
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
            >
              <el-option
                v-for="shop in shopOptions"
                :key="shop.value"
                :label="shop.label"
                :value="shop.value"
              />
            </el-select>
          </div>

          <!-- 时间选择区域 -->
          <div class="config-section">
            <div class="section-title">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                <line x1="16" y1="2" x2="16" y2="6"/>
                <line x1="8" y1="2" x2="8" y2="6"/>
                <line x1="3" y1="10" x2="21" y2="10"/>
              </svg>
              <span>时间范围</span>
            </div>

            <div class="date-range-container">
              <div class="date-range-picker">
                <label class="date-range-label">选择日期范围</label>
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  class="date-range-input"
                  size="large"
                />
              </div>

              <!-- 快速选择 -->
              <div class="quick-select">
                <span class="quick-label">快速选择：</span>
                <div class="quick-buttons">
                  <button
                    @click="setQuickDate('today')"
                    class="quick-btn"
                    :class="{ active: isQuickDateActive('today') }"
                  >
                    今天
                  </button>
                  <button
                    @click="setQuickDate('yesterday')"
                    class="quick-btn"
                    :class="{ active: isQuickDateActive('yesterday') }"
                  >
                    昨天
                  </button>
                  <button
                    @click="setQuickDate('week')"
                    class="quick-btn"
                    :class="{ active: isQuickDateActive('week') }"
                  >
                    本周
                  </button>
                  <button
                    @click="setQuickDate('month')"
                    class="quick-btn"
                    :class="{ active: isQuickDateActive('month') }"
                  >
                    本月
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 操作区域 -->
      <section class="action-panel">
        <div class="action-primary">
          <button
            @click="handleGetChatGroups"
            :disabled="loading || loadingShops || !fromDate || !toDate || !selectedShopIds.length"
            class="extract-btn"
            :class="{ 'loading': loading, 'disabled': loadingShops || !fromDate || !toDate || !selectedShopIds.length }"
          >
            <div class="btn-content">
              <div class="btn-icon">
                <svg v-if="!loading" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                  <path d="M8 9h8"/>
                  <path d="M8 13h6"/>
                </svg>
                <div v-else class="spinner"></div>
              </div>
              <div class="btn-text">
                <span class="btn-title">{{ loading ? '正在提取数据...' : '开始提取数据' }}</span>
                <span class="btn-subtitle">生成Excel文件并自动下载</span>
              </div>
            </div>
          </button>
        </div>
      </section>

      <!-- 导出历史区域 -->
      <section v-if="showHistory" class="history-panel">
        <div class="panel-header">
          <h3>导出历史</h3>
          <p>{{ historyData.length }} 条导出记录</p>
          <button @click="showHistory = false" class="close-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <div class="table-wrapper">
          <el-table
            :data="historyData"
            class="history-table"
            stripe
          >
            <el-table-column prop="_id" label="导出时间" width="150">
              <template #default="{ row }">
                <div class="export-time">{{ row._id }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="count" label="数据量" width="100">
              <template #default="{ row }">
                <span class="data-count">{{ row.count }} 条</span>
              </template>
            </el-table-column>
            <el-table-column prop="stores" label="涉及店铺" min-width="200">
              <template #default="{ row }">
                <div class="stores-list">
                  <el-tag
                    v-for="store in row.stores.slice(0, 3)"
                    :key="store"
                    size="small"
                    class="store-tag"
                  >
                    {{ store || '未知店铺' }}
                  </el-tag>
                  <span v-if="row.stores.length > 3" class="more-stores">
                    +{{ row.stores.length - 3 }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="dateRangeStr" label="数据时间范围" width="150">
              <template #default="{ row }">
                <div class="table-date-range">{{ row.dateRangeStr || '未知' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  :loading="downloadingId === row._id"
                  @click="handleRedownload(row)"
                >
                  <svg v-if="downloadingId !== row._id" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 14px; height: 14px; margin-right: 4px;">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                    <polyline points="7,10 12,15 17,10"/>
                    <line x1="12" y1="15" x2="12" y2="3"/>
                  </svg>
                  {{ downloadingId === row._id ? '下载中...' : '下载' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div v-if="historyPagination.total > 0" class="pagination-wrapper">
          <el-pagination
            v-model:current-page="historyPagination.page"
            v-model:page-size="historyPagination.pageSize"
            :total="historyPagination.total"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleHistorySizeChange"
            @current-change="handleHistoryPageChange"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '../stores/auth'
import { exportAPI, dataAPI } from '../api'
import * as XLSX from 'xlsx-js-style'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const showHistory = ref(false)
const historyData = ref([])
const downloadingId = ref(null)

// 历史记录分页数据
const historyPagination = ref({
  page: 1,
  pageSize: 20,
  total: 0
})

// 店铺选择数据
const selectedShopIds = ref([])
const shopOptions = ref([])
const loadingShops = ref(false)

// 时间选择器数据
const dateRange = ref([])

// 计算属性：从dateRange中获取开始和结束日期
const fromDate = computed(() => {
  return dateRange.value && dateRange.value.length === 2 ? dateRange.value[0] : ''
})

const toDate = computed(() => {
  return dateRange.value && dateRange.value.length === 2 ? dateRange.value[1] : ''
})

// 日期限制函数：不能选择超过今天的日期
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

// 快速日期选择
const setQuickDate = (type) => {
  const today = new Date()
  const todayStr = today.toISOString().split('T')[0]

  switch (type) {
    case 'today':
      // 今天：开始和结束都是今天
      dateRange.value = [todayStr, todayStr]
      break
    case 'yesterday':
      // 昨天：开始和结束都是昨天
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().split('T')[0]
      dateRange.value = [yesterdayStr, yesterdayStr]
      break
    case 'week':
      // 本周：从本周一到今天（中国习惯周一为一周开始）
      const weekStart = new Date(today)
      const dayOfWeek = today.getDay() // 0=周日, 1=周一, ..., 6=周六
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // 周日需要回退6天到周一
      weekStart.setDate(today.getDate() - daysToMonday)
      const weekStartStr = weekStart.toISOString().split('T')[0]
      dateRange.value = [weekStartStr, todayStr]
      break
    case 'month':
      // 本月：从本月1号到今天
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const monthStartStr = monthStart.toISOString().split('T')[0]
      dateRange.value = [monthStartStr, todayStr]
      break
  }
}

// 检查快速日期按钮是否激活
const isQuickDateActive = (type) => {
  if (!dateRange.value || dateRange.value.length !== 2) return false

  const [start, end] = dateRange.value
  const today = new Date()
  const todayStr = today.toISOString().split('T')[0]

  switch (type) {
    case 'today':
      // 今天：检查是否开始和结束都是今天
      return start === todayStr && end === todayStr
    case 'yesterday':
      // 昨天：检查是否开始和结束都是昨天
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = yesterday.toISOString().split('T')[0]
      return start === yesterdayStr && end === yesterdayStr
    case 'week':
      // 本周：检查是否从本周一到今天
      const weekStart = new Date(today)
      const dayOfWeek = today.getDay() // 0=周日, 1=周一, ..., 6=周六
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1 // 周日需要回退6天到周一
      weekStart.setDate(today.getDate() - daysToMonday)
      const weekStartStr = weekStart.toISOString().split('T')[0]
      return start === weekStartStr && end === todayStr
    case 'month':
      // 本月：检查是否从本月1号到今天
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const monthStartStr = monthStart.toISOString().split('T')[0]
      return start === monthStartStr && end === todayStr
    default:
      return false
  }
}

// 获取店铺列表
const loadShops = async () => {
  try {
    loadingShops.value = true
    console.log('开始获取店铺列表...')

    const result = await exportAPI.getShops()
    console.log('店铺列表API响应:', result)

    if (result.success && result.data) {
      shopOptions.value = result.data.map(shop => ({
        value: shop.shopId,
        label: shop.fullName,
        shopName: shop.shopName,
        branchName: shop.branchName
      }))

      // 默认选择所有店铺
      selectedShopIds.value = shopOptions.value.map(shop => shop.value)

      console.log(`成功获取 ${shopOptions.value.length} 个店铺，默认全选`)
      ElMessage.success(`成功获取 ${shopOptions.value.length} 个店铺`)
    } else {
      console.error('获取店铺列表失败:', result.message)
      ElMessage.error(result.message || '获取店铺列表失败')
    }
  } catch (error) {
    console.error('获取店铺列表失败:', error)
    ElMessage.error('获取店铺列表失败，请稍后重试')
  } finally {
    loadingShops.value = false
  }
}

// 获取用户数据并导出Excel
const handleGetChatGroups = async () => {
  if (!fromDate.value || !toDate.value || !selectedShopIds.value.length) {
    ElMessage.warning('请选择店铺和时间范围')
    return
  }

  try {
    loading.value = true

    // 显示加载消息
    const loadingMessage = ElMessage.info({
      message: '正在获取用户数据，请耐心等待...',
      duration: 0
    })

    console.log('开始获取用户数据...')
    console.log('选择的店铺:', selectedShopIds.value)
    console.log('时间范围:', fromDate.value, '到', toDate.value)

    let data = []
    let successCount = 0
    let errorCount = 0

    // 遍历每个店铺获取数据
    for (const shopId of selectedShopIds.value) {
      try {
        console.log(`正在获取店铺 ${shopId} 的数据...`)

        const result = await dataAPI.getChatGroups({
          shopId: shopId,
          fromDate: fromDate.value,
          toDate: toDate.value
        })

        if (result.success && result.data) {
          data = data.concat(result.data)
          successCount++
          console.log(`店铺 ${shopId} 数据获取成功，获得 ${result.data.length} 条记录`)
        } else {
          errorCount++
          console.error(`店铺 ${shopId} 数据获取失败:`, result.message)
        }
      } catch (shopError) {
        errorCount++
        console.error(`店铺 ${shopId} 数据获取异常:`, shopError)
      }
    }

    console.log(`数据获取完成，总计 ${data.length} 条记录`)

    if (!data || data.length === 0) {
      ElMessage.warning('没有数据可以导出')
      return
    }

    // 关闭加载消息
    if (loadingMessage) {
      loadingMessage.close()
    }

    // 显示Excel生成消息
    const excelMessage = ElMessage.info({
      message: '正在生成Excel文件...',
      duration: 0
    })

    // 门店名称格式化函数
    const formatStoreName = (storeName) => {
      if (!storeName || typeof storeName !== 'string') {
        return ''
      }

      // 处理今识医疗美容相关的门店名称
      if (storeName.includes('今识医疗美容')) {
        // 匹配括号内的内容（支持中文括号和英文括号）
        const match = storeName.match(/今识医疗美容[（(]([^）)]+)[）)]/)
        if (match && match[1]) {
          return match[1] // 返回括号内的内容，如"日月光店"、"陆家嘴店"
        }
      }

      // 如果不匹配特殊规则，返回原始名称
      return storeName.trim()
    }

    try {
      // 使用xlsx-js-style库
      console.log('开始使用xlsx-js-style库...')
      console.log('xlsx-js-style库准备就绪')

      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      console.log('工作簿创建成功')

      // 定义需要显示的字段和中文表头映射
      const headerMapping = {
        'date': '日期',
        'time': '时间',
        'messageSource': '私信来源',
        'customerService': '客服人员',
        'store': '门店',
        'customerType': '客人类型',
        'isValid': '是否有效',
        'firstMessage': '第一句话内容',
        'consultationType': '咨询类型',
        'consultationChannel': '咨询渠道',
        'platformNickname': '平台备注昵称',
        'contactInfo': '联系方式',
        'consultationLevel1': '咨询一级',
        'consultationLevel2': '咨询二级',
        'consultationLevel3': '咨询三级',
        'addWechat': '添加微信',
        'appointmentTime': '预约时间',
        'remarks': '备注',
        'contactCount': '留联数'
      }

      // 需要排除的字段
      const excludeFields = ['userId', 'shopId', 'merchantId', 'storeId', 'collectedAt', 'rawData']

      // 转换数据，只保留需要的字段并替换为中文表头
      const transformedData = data.map(row => {
        const newRow = {}
        Object.keys(row).forEach(key => {
          // 跳过不需要的字段
          if (excludeFields.includes(key)) {
            return
          }

          const chineseHeader = headerMapping[key] || key
          let value = row[key] || ''

          // 对门店字段进行特殊处理
          if (key === 'store') {
            value = formatStoreName(value)
          }

          newRow[chineseHeader] = value
        })
        return newRow
      })

      console.log('数据转换完成，样本数据:', transformedData[0])

      // 将转换后的数据创建为工作表
      const worksheet = XLSX.utils.json_to_sheet(transformedData)
      console.log('工作表创建成功')

      // 设置列宽（对应19个字段）
      const colWidths = [
        { wch: 14 }, // 日期
        { wch: 12 }, // 时间
        { wch: 15 }, // 私信来源
        { wch: 15 }, // 客服人员
        { wch: 25 }, // 门店
        { wch: 15 }, // 客人类型
        { wch: 12 }, // 是否有效
        { wch: 45 }, // 第一句话内容
        { wch: 15 }, // 咨询类型
        { wch: 15 }, // 咨询渠道
        { wch: 20 }, // 平台备注昵称
        { wch: 18 }, // 联系方式
        { wch: 15 }, // 咨询一级
        { wch: 15 }, // 咨询二级
        { wch: 15 }, // 咨询三级
        { wch: 12 }, // 添加微信
        { wch: 18 }, // 预约时间
        { wch: 30 }, // 备注
        { wch: 10 }  // 留联数
      ]
      worksheet['!cols'] = colWidths

      // 设置表头样式和所有单元格边框
      const range = XLSX.utils.decode_range(worksheet['!ref'])

      // 为所有单元格添加边框和基本样式
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col })
          if (!worksheet[cellAddress]) {
            worksheet[cellAddress] = { t: 's', v: '' }
          }

          // 基本样式：边框
          const baseStyle = {
            border: {
              top: { style: "thin", color: { rgb: "000000" } },
              bottom: { style: "thin", color: { rgb: "000000" } },
              left: { style: "thin", color: { rgb: "000000" } },
              right: { style: "thin", color: { rgb: "000000" } }
            },
            alignment: { vertical: "center", wrapText: true }
          }

          // 表头特殊样式
          if (row === 0) {
            worksheet[cellAddress].s = {
              ...baseStyle,
              font: { bold: true, sz: 12, color: { rgb: "FFFFFF" } },
              fill: { patternType: "solid", fgColor: { rgb: "4472C4" } },
              alignment: { horizontal: "center", vertical: "center", wrapText: true }
            }
          } else {
            worksheet[cellAddress].s = {
              ...baseStyle,
              font: { sz: 10 },
              alignment: { horizontal: "left", vertical: "center", wrapText: true }
            }
          }
        }
      }

      // 设置表头行高
      worksheet['!rows'] = [
        { hpt: 25 }, // 表头行高度为25磅
        ...Array(range.e.r).fill({ hpt: 18 }) // 数据行高度为18磅
      ]

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '顾客数据')
      console.log('工作表添加到工作簿成功')

      // 生成文件名（包含时间范围）
      const now = new Date()
      const dateStr = now.toISOString().split('T')[0]
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-')
      const dateRange = fromDate.value === toDate.value ? fromDate.value : `${fromDate.value}至${toDate.value}`
      const fileName = `大众点评顾客数据_${dateRange}_${dateStr}_${timeStr}.xlsx`
      console.log('生成文件名:', fileName)

      // 导出Excel文件
      console.log('开始导出Excel文件...')
      XLSX.writeFile(workbook, fileName)
      console.log('Excel文件导出完成')

      // 关闭Excel生成消息
      excelMessage.close()

      ElMessage.success(`Excel文件生成成功！成功: ${successCount}，失败: ${errorCount}`)
    } catch (excelError) {
      console.error('Excel生成失败:', excelError)
      excelMessage.close()
      ElMessage.error('Excel文件生成失败')
    }

  } catch (error) {
    console.error('获取会话信息失败:', error)

    let errorMessage = '获取用户数据失败，请稍后重试'
    if (error.message === '请求超时') {
      errorMessage = '请求超时（超过10分钟），请检查网络连接后重试'
    } else if (error.response) {
      errorMessage = `请求失败: ${error.response.status} ${error.response.statusText}`
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 查看导出历史
const handleViewHistory = async () => {
  try {
    loading.value = true
    historyPagination.value.page = 1
    await loadHistoryData()
    showHistory.value = true
  } catch (error) {
    ElMessage.error('获取导出历史失败')
  } finally {
    loading.value = false
  }
}

// 加载历史数据
const loadHistoryData = async () => {
  try {
    const result = await exportAPI.getHistory({
      page: historyPagination.value.page,
      pageSize: historyPagination.value.pageSize
    })

    if (result.success) {
      historyData.value = result.data
      historyPagination.value.total = result.total
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('加载历史数据失败:', error)
    ElMessage.error('加载历史数据失败')
  }
}

// 分页大小改变
const handleHistorySizeChange = (newSize) => {
  historyPagination.value.pageSize = newSize
  historyPagination.value.page = 1
  loadHistoryData()
}

// 页码改变
const handleHistoryPageChange = (newPage) => {
  historyPagination.value.page = newPage
  loadHistoryData()
}

// 重新下载历史数据
const handleRedownload = async (row) => {
  try {
    downloadingId.value = row._id

    const response = await exportAPI.redownload(row._id)

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `历史导出_${row._id.replace(/[:.]/g, '-').replace(' ', '_')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('重新下载失败:', error)
    ElMessage.error('重新下载失败')
  } finally {
    downloadingId.value = null
  }
}



// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await authStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    // 用户取消操作
  }
}

onMounted(async () => {
  // 检查认证状态
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 获取店铺列表（会自动设置默认选择所有店铺）
  await loadShops()

  // 设置默认时间范围为今天
  const today = new Date().toISOString().split('T')[0]
  dateRange.value = [today, today]
})
</script>

<style scoped>
/* 全新的侧边栏布局 */
.functions-page {
  display: flex;
  min-height: 100vh;
  background: var(--color-gray-50);
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 100;
}

.sidebar-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--color-gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary) 0%, #4f46e5 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.logo svg {
  width: 24px;
  height: 24px;
  color: var(--color-white);
}

.logo-text h1 {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: 2px;
}

.logo-text p {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg);
}

.nav-section {
  margin-bottom: var(--spacing-xl);
}

.nav-title {
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--color-gray-500);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-md);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-xs);
}

.nav-item.active {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.nav-item svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--color-gray-200);
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-gray-100);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.logout-btn svg {
  width: 16px;
  height: 16px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 顶部状态栏 */
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-xs);
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--color-gray-600);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-success);
  color: var(--color-white);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: var(--color-white);
  border-radius: 50%;
}

/* 配置面板 */
.config-panel {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.panel-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

.panel-header h3 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-xs);
}

.panel-header p {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.config-form {
  padding: var(--spacing-xl);
}

.config-section {
  margin-bottom: var(--spacing-xl);
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--color-gray-900);
}

.section-title svg {
  width: 20px;
  height: 20px;
  color: var(--color-primary);
}

.selection-count {
  margin-left: auto;
  background: var(--color-primary-light);
  color: var(--color-primary);
  font-size: var(--text-xs);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
}

/* Element Plus 组件样式重写 */
.shop-selector {
  width: 100%;
}

.shop-selector :deep(.el-select__wrapper) {
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  min-height: 48px;
  transition: all var(--transition-normal);
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.shop-selector :deep(.el-select__wrapper.is-focused) {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), var(--shadow-md);
}

.shop-selector :deep(.el-select__placeholder) {
  color: var(--color-gray-500);
  font-size: var(--text-sm);
}

.shop-selector :deep(.el-select__input) {
  font-size: var(--text-sm);
  color: var(--color-gray-900);
}

.shop-selector :deep(.el-select__tags) {
  max-width: calc(100% - 30px);
}

.shop-selector :deep(.el-tag) {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  margin-right: var(--spacing-xs);
}

.shop-selector :deep(.el-tag .el-tag__close) {
  color: var(--color-primary);
}

.shop-selector :deep(.el-tag .el-tag__close):hover {
  background: var(--color-primary);
  color: var(--color-white);
}

/* 下拉面板样式 */
.shop-selector :deep(.el-select-dropdown) {
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-xs);
}

.shop-selector :deep(.el-select-dropdown__item) {
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  transition: all var(--transition-fast);
}

.shop-selector :deep(.el-select-dropdown__item:hover) {
  background: var(--color-gray-50);
  color: var(--color-gray-900);
}

.shop-selector :deep(.el-select-dropdown__item.is-selected) {
  background: var(--color-primary-light);
  color: var(--color-primary);
  font-weight: 600;
}

.shop-selector :deep(.el-select-dropdown__item.is-selected::after) {
  color: var(--color-primary);
}

/* 日期范围选择器容器 - 左右布局 */
.date-range-container {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.date-range-picker {
  flex: 1;
  min-width: 280px;
}

.date-range-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-sm);
}

.date-range-input {
  width: 100%;
}

/* 日期范围选择器样式 */
.date-range-input :deep(.el-input__wrapper) {
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  height: 52px;
  transition: all var(--transition-normal);
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
  padding: 0 var(--spacing-md);
}

.date-range-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), var(--shadow-md);
}

.date-range-input :deep(.el-input__inner) {
  font-size: var(--text-base);
  color: var(--color-gray-900);
  font-weight: 500;
  height: 48px;
  line-height: 48px;
}

.date-range-input :deep(.el-input__prefix) {
  color: var(--color-gray-500);
}

.date-range-input :deep(.el-input__suffix) {
  color: var(--color-gray-500);
}

.date-range-input :deep(.el-range-separator) {
  color: var(--color-gray-600);
  font-weight: 500;
}

/* 日期范围选择器弹出面板 */
.date-range-input :deep(.el-picker-panel) {
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.date-range-input :deep(.el-picker-panel__header) {
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  padding: var(--spacing-md);
}

.date-range-input :deep(.el-picker-panel__header-label) {
  color: var(--color-gray-900);
  font-weight: 600;
}

.date-range-input :deep(.el-picker-panel__prev-btn),
.date-range-input :deep(.el-picker-panel__next-btn) {
  color: var(--color-gray-600);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.date-range-input :deep(.el-picker-panel__prev-btn):hover,
.date-range-input :deep(.el-picker-panel__next-btn):hover {
  background: var(--color-gray-100);
  color: var(--color-gray-900);
}

.date-range-input :deep(.el-date-table td) {
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.date-range-input :deep(.el-date-table td.available:hover) {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.date-range-input :deep(.el-date-table td.current) {
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: 600;
}

.date-range-input :deep(.el-date-table td.today) {
  color: var(--color-primary);
  font-weight: 600;
}

.date-range-input :deep(.el-date-table td.in-range) {
  background: var(--color-primary-light);
  color: var(--color-primary);
}

.date-range-input :deep(.el-date-table td.start-date),
.date-range-input :deep(.el-date-table td.end-date) {
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: 600;
}

.date-range-input :deep(.el-picker-panel__footer) {
  background: var(--color-gray-50);
  border-top: 1px solid var(--color-gray-200);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  padding: var(--spacing-sm) var(--spacing-md);
}

.date-range-input :deep(.el-picker-panel__link-btn) {
  color: var(--color-primary);
  font-weight: 500;
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: all var(--transition-fast);
}

.date-range-input :deep(.el-picker-panel__link-btn):hover {
  background: var(--color-primary-light);
}

/* 快速选择按钮区域 */
.quick-select {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.quick-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-700);
  white-space: nowrap;
}

.quick-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.quick-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--color-white);
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
  text-align: center;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
}

.quick-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.quick-btn.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

/* 操作区域 */
.action-panel {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  padding: var(--spacing-xl);
}

.extract-btn {
  width: 100%;
  background: linear-gradient(135deg, var(--color-primary) 0%, #4f46e5 100%);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.extract-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.extract-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.extract-btn:not(.disabled):active::before {
  left: 100%;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.btn-icon {
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.btn-icon svg {
  width: 32px;
  height: 32px;
  color: var(--color-white);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--color-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-text {
  flex: 1;
  text-align: left;
}

.btn-title {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--color-white);
  margin-bottom: var(--spacing-xs);
}

.btn-subtitle {
  display: block;
  font-size: var(--text-base);
  color: rgba(255, 255, 255, 0.8);
}

/* 历史数据面板 */
.history-panel {
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
}

.history-panel .panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.close-btn {
  width: 32px;
  height: 32px;
  background: var(--color-gray-200);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.close-btn svg {
  width: 16px;
  height: 16px;
  color: var(--color-gray-600);
}

.table-wrapper {
  padding: var(--spacing-xl);
  overflow-x: auto;
}

/* 导出历史相关样式 */
.export-time {
  font-weight: 500;
  color: var(--color-gray-900);
}

.data-count {
  font-weight: 600;
  color: var(--color-blue-600);
}

.stores-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  align-items: center;
}

.store-tag {
  margin: 0;
}

.more-stores {
  font-size: var(--text-sm);
  color: var(--color-gray-500);
  margin-left: var(--spacing-xs);
}

.table-date-range {
  font-size: var(--text-sm);
  color: var(--color-gray-600);
}

.pagination-wrapper {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: center;
}

.history-table {
  width: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.history-table :deep(.el-table__header-wrapper) {
  background: var(--color-gray-50);
}

.history-table :deep(.el-table__header th) {
  background: var(--color-gray-50);
  color: var(--color-gray-700);
  font-weight: 600;
  font-size: var(--text-sm);
  border-bottom: 2px solid var(--color-gray-200);
  padding: var(--spacing-md);
  text-align: left;
}

.history-table :deep(.el-table__header th.is-leaf) {
  border-bottom: 2px solid var(--color-gray-200);
}

.history-table :deep(.el-table__body) {
  background: var(--color-white);
}

.history-table :deep(.el-table__body td) {
  border-bottom: 1px solid var(--color-gray-100);
  padding: var(--spacing-md);
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  vertical-align: middle;
}

.history-table :deep(.el-table__body tr) {
  transition: all var(--transition-fast);
}

.history-table :deep(.el-table__body tr:hover) {
  background: var(--color-gray-50);
}

.history-table :deep(.el-table__body tr:hover td) {
  background: transparent;
}

.history-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped) {
  background: var(--color-gray-25);
}

.history-table :deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover) {
  background: var(--color-gray-50);
}

.history-table :deep(.el-table__empty-block) {
  background: var(--color-white);
  color: var(--color-gray-500);
  font-size: var(--text-sm);
}

.history-table :deep(.el-table__empty-text) {
  color: var(--color-gray-500);
}

.message-content {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: var(--text-sm);
  color: var(--color-gray-700);
}

.status-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
}

.status-tag.valid {
  background: var(--color-success);
  color: var(--color-white);
}

.status-tag.invalid {
  background: var(--color-gray-300);
  color: var(--color-gray-700);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }

  .main-content {
    margin-left: 0;
    padding: var(--spacing-lg);
  }

  .page-title {
    font-size: var(--text-2xl);
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .date-range-container {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .date-range-picker {
    min-width: auto;
  }

  .quick-select {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .quick-label {
    font-size: var(--text-xs);
  }

  .quick-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-md);
    gap: var(--spacing-lg);
  }

  .page-title {
    font-size: var(--text-xl);
  }

  .panel-header,
  .config-form,
  .action-panel,
  .table-wrapper {
    padding: var(--spacing-lg);
  }

  .btn-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .btn-icon {
    width: 56px;
    height: 56px;
  }

  .btn-icon svg {
    width: 28px;
    height: 28px;
  }

  .spinner {
    width: 28px;
    height: 28px;
  }

  .btn-title {
    font-size: var(--text-lg);
  }

  .btn-subtitle {
    font-size: var(--text-sm);
  }

  .quick-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .quick-btn {
    flex: 1;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-sm);
    gap: var(--spacing-md);
  }

  .panel-header,
  .config-form,
  .action-panel,
  .table-wrapper {
    padding: var(--spacing-md);
  }

  .section-title {
    font-size: var(--text-sm);
  }

  .section-title svg {
    width: 16px;
    height: 16px;
  }

  .btn-icon {
    width: 48px;
    height: 48px;
  }

  .btn-icon svg {
    width: 24px;
    height: 24px;
  }

  .spinner {
    width: 24px;
    height: 24px;
  }

  .btn-title {
    font-size: var(--text-base);
  }

  .btn-subtitle {
    font-size: var(--text-xs);
  }

  .quick-buttons {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .quick-btn {
    width: 100%;
  }

  .message-content {
    max-width: 120px;
  }
}
</style>