<template>
  <div class="login-page">
    <!-- 左侧品牌区域 -->
    <div class="brand-section">
      <div class="brand-content">
        <div class="brand-logo">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
          </svg>
        </div>
        <h1 class="brand-title">大众点评<br>数据提取工具</h1>
        <p class="brand-description">安全高效的商户数据管理平台，助力您的业务数据分析</p>

        <!-- 特性列表 -->
        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span>安全可靠的数据提取</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
              </svg>
            </div>
            <span>快速高效的处理</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
            </div>
            <span>Excel格式导出</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="login-section">
      <div class="login-container">
        <div class="login-header">
          <h2>{{ currentMode === 'add' ? '添加Cookie配置' : '登录' }}</h2>
          <p>{{ getModeDescription() }}</p>
        </div>

        <!-- 模式切换按钮 -->
        <div class="mode-switcher">
          <el-button-group>
            <el-button
              :type="currentMode === 'add' ? 'primary' : ''"
              @click="switchMode('add')"
            >
              添加配置
            </el-button>
            <el-button
              :type="currentMode === 'login' ? 'primary' : ''"
              @click="switchMode('login')"
            >
              进行登录
            </el-button>
          </el-button-group>
        </div>

        <!-- 添加配置模式 -->
        <div v-if="currentMode === 'add'" class="config-add-mode">
          <el-form
            ref="addFormRef"
            :model="addForm"
            :rules="addRules"
            class="add-form"
          >
            <el-form-item prop="cookies">
              <label class="form-label">Cookie信息</label>
              <el-input
                v-model="addForm.cookies"
                type="textarea"
                :rows="4"
                placeholder="请粘贴从浏览器开发者工具中获取的Cookie信息..."
                class="cookie-input"
              />
            </el-form-item>

            <el-form-item prop="name">
              <label class="form-label">配置名称（可选）</label>
              <el-input
                v-model="addForm.name"
                placeholder="请输入配置名称，留空将自动生成"
                class="name-input"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="authStore.loading"
                @click="handleAddConfig"
                class="login-btn"
              >
                {{ authStore.loading ? '添加中...' : '添加配置' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 显示生成的密码 -->
          <div v-if="generatedPassword" class="generated-password-display">
            <el-alert
              title="🎉 配置添加成功！"
              type="success"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="password-success-content">
                  <h4 class="password-title">⚠️ 请务必记住您的登录密码</h4>
                  <div class="password-display">
                    <el-input
                      v-model="generatedPassword"
                      readonly
                      size="large"
                      class="password-input"
                    >
                      <template #append>
                        <el-button @click="copyGeneratedPassword" :icon="CopyDocument" type="primary">
                          复制密码
                        </el-button>
                      </template>
                    </el-input>
                  </div>
                  <div class="password-warnings">
                    <p class="warning-text">🔐 此密码用于登录该配置，请妥善保存</p>
                    <p class="warning-text">📝 建议将密码保存到密码管理器或安全的地方</p>
                    <p class="warning-text">❌ 如果忘记密码，需要删除配置重新创建</p>
                  </div>

                  <div class="password-actions">
                    <el-button
                      type="primary"
                      size="large"
                      @click="handlePasswordSaved"
                    >
                      我已保存密码，继续登录
                    </el-button>
                    <el-button
                      size="large"
                      @click="handleAddAnother"
                    >
                      添加其他配置
                    </el-button>
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </div>

        <!-- 登录模式 -->
        <div v-else-if="currentMode === 'login'" class="config-login-mode">
          <el-form
            ref="selectFormRef"
            :model="selectForm"
            :rules="selectRules"
            class="select-form"
          >
            <el-form-item prop="password">
              <label class="form-label">登录密码</label>
              <el-input
                v-model="selectForm.password"
                type="password"
                placeholder="请输入配置密码"
                show-password
                size="large"
                class="password-input"
                @keyup.enter="handlePasswordLogin"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="authStore.loading"
                @click="handlePasswordLogin"
                class="login-btn"
                size="large"
              >
                {{ authStore.loading ? '登录中...' : '登录' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>



        <!-- 帮助信息 -->
        <div class="help-info">
          <details class="help-toggle">
            <summary>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
              如何获取Cookie信息？
            </summary>
            <div class="help-steps">
              <div class="step">1. 登录大众点评商户平台</div>
              <div class="step">2. 按F12打开开发者工具</div>
              <div class="step">3. 切换到Network标签</div>
              <div class="step">4. 刷新页面，找到任意请求</div>
              <div class="step">5. 复制请求头中的Cookie字段</div>
            </div>
          </details>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'
import { authAPI } from '../api'

const router = useRouter()
const authStore = useAuthStore()

// 当前模式：add（添加配置）、login（登录）
const currentMode = ref('add')
const generatedPassword = ref('')

// 表单引用
const addFormRef = ref()
const selectFormRef = ref()

// 添加配置表单
const addForm = reactive({
  name: '',
  cookies: ''
})

// 选择配置表单
const selectForm = reactive({
  password: ''
})

// 表单验证规则
const addRules = {
  cookies: [
    { required: true, message: '请输入Cookie信息', trigger: 'blur' },
    { min: 100, message: 'Cookie信息长度不能少于100个字符', trigger: 'blur' },
    {
      validator: (_rule, value, callback) => {
        if (value && !value.includes('mpmerchant_portal_shopid')) {
          callback(new Error('Cookie信息中必须包含商户店铺ID信息'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const selectRules = {
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 计算属性
const getModeDescription = () => {
  switch (currentMode.value) {
    case 'add':
      return '添加新的Cookie配置，系统将自动生成登录密码'
    case 'login':
      return '请输入配置密码进行登录'
    default:
      return '请选择操作方式'
  }
}

// 方法
const switchMode = (mode) => {
  currentMode.value = mode
  // 清空表单
  if (mode === 'login') {
    selectForm.password = ''
  } else if (mode === 'add') {
    Object.assign(addForm, {
      name: '',
      cookies: ''
    })
    generatedPassword.value = ''
  }
}







const handleAddConfig = async () => {
  try {
    const valid = await addFormRef.value.validate()
    if (!valid) return

    // 从Cookie中提取商户ID
    const merchantId = extractMerchantId(addForm.cookies)
    if (!merchantId) {
      ElMessage.error('无法从Cookie中提取商户ID，请检查Cookie信息是否完整')
      return
    }

    // 生成随机密码
    const password = generateSimplePassword()

    // 生成配置名称（如果用户没有输入）
    const configName = addForm.name.trim() || `配置-${Date.now()}`

    // 只创建配置，不直接登录
    const result = await authAPI.createCookieConfig({
      name: configName,
      description: '自动生成的配置',
      cookies: addForm.cookies,
      password: password
    })

    if (result.success) {
      generatedPassword.value = password
      ElMessage.success('配置添加成功！请务必记住您的登录密码')

      // 清空表单，但保持在添加模式
      Object.assign(addForm, {
        name: '',
        cookies: ''
      })
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('添加配置失败:', error)
    ElMessage.error('添加配置失败，请检查输入信息')
  }
}

// 生成简单密码的辅助函数
const generateSimplePassword = () => {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789'
  let password = ''
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

// 复制生成的密码
const copyGeneratedPassword = async () => {
  try {
    await navigator.clipboard.writeText(generatedPassword.value)
    ElMessage.success('密码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制密码')
  }
}

// 用户确认已保存密码，切换到登录模式
const handlePasswordSaved = () => {
  // 切换到登录模式
  currentMode.value = 'login'

  // 清空生成的密码显示
  generatedPassword.value = ''

  ElMessage.info('请输入刚才保存的密码进行登录')
}

// 添加其他配置
const handleAddAnother = () => {
  // 清空生成的密码显示，保持在添加模式
  generatedPassword.value = ''
  ElMessage.info('您可以继续添加其他配置')
}



// 使用密码匹配登录
const handlePasswordLogin = async () => {
  try {
    const valid = await selectFormRef.value.validate()
    if (!valid) return

    const result = await authStore.loginWithPassword(selectForm.password)

    if (result.success) {
      ElMessage.success(result.message)
      router.push('/functions')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('密码登录失败:', error)
    ElMessage.error('登录失败，请检查密码是否正确')
  }
}





// 从Cookie中提取商户ID
const extractMerchantId = (cookieString) => {
  const match = cookieString.match(/mpmerchant_portal_shopid=([^;]+)/)
  return match ? match[1] : null
}

onMounted(async () => {
  // 首先检查当前会话是否有效
  await authStore.checkAuth()
  if (authStore.isAuthenticated) {
    router.push('/functions')
    return
  }
})
</script>

<style scoped>
/* 全新的左右分栏布局 */
.login-page {
  min-height: 100vh;
  display: flex;
  background: var(--color-white);
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, var(--color-primary) 0%, #4f46e5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.brand-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: var(--color-white);
  max-width: 400px;
}

.brand-logo {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-xl);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.brand-logo svg {
  width: 40px;
  height: 40px;
  color: var(--color-white);
}

.brand-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.brand-description {
  font-size: var(--text-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-2xl);
  line-height: 1.6;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: var(--text-base);
  font-weight: 500;
}

.feature-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.feature-icon svg {
  width: 16px;
  height: 16px;
  color: var(--color-white);
}

/* 右侧登录区域 */
.login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  background: var(--color-gray-50);
}

.login-container {
  width: 100%;
  max-width: 480px;
  background: var(--color-white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  backdrop-filter: blur(10px);
  position: relative;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, #4f46e5 100%);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.login-header {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-xl) 0;
  margin-bottom: var(--spacing-lg);
}

.login-header h2 {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-xs);
}

.login-header p {
  font-size: var(--text-base);
  color: var(--color-gray-600);
  margin: 0;
  line-height: 1.5;
}

.login-form {
  margin-bottom: var(--spacing-xl);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-xs);
}

/* Cookie输入框特殊样式 - 覆盖全局样式 */
.login-container .cookie-input :deep(.el-textarea__inner) {
  border: 2px solid var(--color-gray-200) !important;
  border-radius: var(--radius-lg) !important;
  font-family: var(--font-family-mono) !important;
  font-size: var(--text-sm) !important;
  line-height: 1.5 !important;
  padding: var(--spacing-md) !important;
  transition: all var(--transition-normal) !important;
  resize: vertical !important;
  min-height: 100px !important;
  background: var(--color-white) !important;
  box-shadow: var(--shadow-sm) !important;
}

.login-container .cookie-input :deep(.el-textarea__inner):focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), var(--shadow-md) !important;
  outline: none !important;
}

.login-container .cookie-input :deep(.el-textarea__inner):hover {
  border-color: var(--color-gray-300) !important;
}

.login-container .cookie-input :deep(.el-textarea__inner)::placeholder {
  color: var(--color-gray-500) !important;
  font-size: var(--text-sm) !important;
}

.login-container .password-input :deep(.el-input__inner)::placeholder,
.login-container .name-input :deep(.el-input__inner)::placeholder {
  color: var(--color-gray-500) !important;
  font-size: var(--text-base) !important;
}

.login-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, var(--color-primary) 0%, #4f46e5 100%);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 600;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  margin-top: var(--spacing-sm);
}

.login-btn:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Element Plus 按钮样式覆盖 */
.login-btn :deep(.el-button) {
  background: transparent;
  border: none;
  color: var(--color-white);
  font-weight: 600;
  width: 100%;
  height: 100%;
}

/* 多Cookie管理样式 */
.mode-switcher {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
  padding: 0 var(--spacing-xl);
}

/* 生成密码显示样式 */
.generated-password-display {
  margin-top: 24px;
}

.password-success-content {
  text-align: center;
}

.password-title {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--color-warning);
  margin: 0 0 20px 0;
  text-align: center;
}

.password-display {
  margin: 20px 0;
}

.password-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  font-size: var(--text-lg);
}

.password-input :deep(.el-input__inner) {
  text-align: center;
  font-size: var(--text-lg);
  font-weight: 700;
  letter-spacing: 2px;
  color: var(--color-primary);
}

.password-warnings {
  margin: 20px 0;
  text-align: left;
  background: var(--color-warning-light);
  padding: 16px;
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-warning);
}

.warning-text {
  margin: 8px 0;
  font-size: var(--text-sm);
  color: var(--color-text);
  line-height: 1.6;
}

.warning-text:first-child {
  margin-top: 0;
}

.warning-text:last-child {
  margin-bottom: 0;
}

.password-actions {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.password-actions .el-button {
  min-width: 160px;
  font-weight: 600;
}

/* 登录模式样式 */
.login-info {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--color-bg-soft);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--color-primary);
}





.mode-switcher .el-button-group {
  box-shadow: var(--shadow-sm);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
}

.config-add-mode,
.config-login-mode {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border-light);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.config-item:last-child {
  border-bottom: none;
}

.config-item:hover {
  background-color: var(--color-bg-soft);
}

.config-item.selected {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.config-info {
  flex: 1;
}

.config-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: 4px;
}

.config-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: var(--text-sm);
  color: var(--color-text-soft);
}

.merchant-name {
  font-weight: 500;
}

.last-used {
  color: var(--color-text-softer);
}

.config-actions {
  display: flex;
  gap: 8px;
}

.config-actions .el-button {
  padding: 4px 8px;
  font-size: var(--text-xs);
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: var(--color-border-light);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.strength-fill {
  height: 100%;
  transition: all var(--transition-normal);
  border-radius: 2px;
}

.strength-fill.很弱 {
  background-color: #f56565;
}

.strength-fill.弱 {
  background-color: #ed8936;
}

.strength-fill.中等 {
  background-color: #ecc94b;
}

.strength-fill.强 {
  background-color: #48bb78;
}

.strength-fill.很强 {
  background-color: #38a169;
}

.strength-text {
  font-size: var(--text-xs);
  font-weight: 500;
  margin-bottom: 4px;
}

.strength-feedback {
  font-size: var(--text-xs);
  color: var(--color-text-soft);
}

.feedback-item {
  margin-bottom: 2px;
}

.feedback-item:before {
  content: "• ";
  color: var(--color-primary);
}

/* 表单样式增强 */
.select-form,
.add-form {
  margin-top: 0;
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-xs);
}

/* 输入框样式优化 - 覆盖全局样式 */
.login-container .password-input :deep(.el-input__wrapper),
.login-container .name-input :deep(.el-input__wrapper) {
  border: 2px solid var(--color-gray-200) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all var(--transition-normal) !important;
  min-height: 52px !important;
  background: var(--color-white) !important;
}

.login-container .password-input :deep(.el-input__wrapper.is-focus),
.login-container .name-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1), var(--shadow-md) !important;
}

.login-container .password-input :deep(.el-input__wrapper:hover),
.login-container .name-input :deep(.el-input__wrapper:hover) {
  border-color: var(--color-gray-300) !important;
}

.login-container .password-input :deep(.el-input__inner),
.login-container .name-input :deep(.el-input__inner) {
  font-size: var(--text-base) !important;
  color: var(--color-gray-900) !important;
  padding: 0 var(--spacing-md) !important;
  font-family: var(--font-family-sans) !important;
  height: 48px !important;
  line-height: 48px !important;
}

/* 密码输入框特殊样式 */
.login-container .password-input :deep(.el-input__suffix) {
  padding-right: var(--spacing-md) !important;
}

.login-container .password-input :deep(.el-input__suffix-inner) {
  color: var(--color-gray-500) !important;
}

.login-container .password-input :deep(.el-input__password) {
  color: var(--color-gray-500) !important;
  cursor: pointer !important;
}

/* 清理重复样式 - 所有输入框样式已在上面统一定义 */

/* Element Plus 按钮样式优化 */
.mode-switcher :deep(.el-button) {
  border: none;
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-normal);
}

.mode-switcher :deep(.el-button--primary) {
  background: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-sm);
}

.mode-switcher :deep(.el-button:not(.el-button--primary):hover) {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mode-switcher .el-button-group {
    display: flex;
    flex-direction: column;
  }

  .config-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .config-actions {
    align-self: flex-end;
  }

  .config-meta {
    flex-wrap: wrap;
  }
}

.login-btn :deep(.el-button):hover,
.login-btn :deep(.el-button):focus {
  background: transparent;
  border: none;
  color: var(--color-white);
}

.login-btn :deep(.el-button.is-loading) {
  background: transparent;
  border: none;
  color: var(--color-white);
}

.login-btn :deep(.el-loading-spinner) {
  color: var(--color-white);
}

.login-btn :deep(.el-loading-spinner .circular) {
  color: var(--color-white);
}

/* 帮助信息 */
.help-info {
  border-top: 1px solid var(--color-gray-200);
  padding-top: var(--spacing-lg);
}

.help-toggle {
  cursor: pointer;
}

.help-toggle summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-gray-700);
  list-style: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.help-toggle summary::-webkit-details-marker {
  display: none;
}

.help-toggle summary svg {
  width: 16px;
  height: 16px;
  color: var(--color-primary);
  flex-shrink: 0;
}

.help-steps {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-gray-50);
  border-radius: var(--radius-md);
}

.step {
  font-size: var(--text-sm);
  color: var(--color-gray-700);
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--color-gray-200);
}

.step:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-page {
    flex-direction: column;
  }

  .brand-section {
    flex: none;
    min-height: 40vh;
    padding: var(--spacing-xl);
  }

  .brand-title {
    font-size: var(--text-3xl);
  }

  .brand-description {
    font-size: var(--text-base);
  }

  .features-list {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
    font-size: var(--text-sm);
  }

  .login-section {
    flex: none;
    padding: var(--spacing-xl);
  }

  .login-container {
    max-width: 420px;
  }
}



@media (max-width: 768px) {
  .brand-section {
    min-height: 30vh;
    padding: var(--spacing-lg);
  }

  .brand-logo {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-lg);
  }

  .brand-logo svg {
    width: 30px;
    height: 30px;
  }

  .brand-title {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
  }

  .brand-description {
    font-size: var(--text-sm);
    margin-bottom: var(--spacing-lg);
  }

  .features-list {
    gap: var(--spacing-sm);
  }

  .login-section {
    padding: var(--spacing-lg);
  }

  .login-container {
    max-width: 380px;
  }

  .login-header {
    padding: var(--spacing-lg) var(--spacing-lg) 0;
  }

  .mode-switcher,
  .config-add-mode,
  .config-login-mode {
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }

  .login-header h2 {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .brand-section {
    min-height: 25vh;
    padding: var(--spacing-md);
  }

  .brand-logo {
    width: 48px;
    height: 48px;
    margin-bottom: var(--spacing-md);
  }

  .brand-logo svg {
    width: 24px;
    height: 24px;
  }

  .brand-title {
    font-size: var(--text-xl);
    margin-bottom: var(--spacing-sm);
  }

  .brand-description {
    font-size: var(--text-xs);
    margin-bottom: var(--spacing-md);
  }

  .features-list {
    display: none;
  }

  .login-section {
    padding: var(--spacing-md);
  }

  .login-container {
    max-width: 100%;
    margin: 0 var(--spacing-sm);
    border-radius: var(--radius-lg);
  }

  .login-header {
    padding: var(--spacing-md) var(--spacing-md) 0;
  }

  .mode-switcher,
  .config-add-mode,
  .config-login-mode {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .config-add-mode,
  .config-login-mode {
    padding-bottom: var(--spacing-md);
  }

  .login-header {
    margin-bottom: var(--spacing-lg);
  }

  .login-header h2 {
    font-size: var(--text-xl);
  }

  .login-header p {
    font-size: var(--text-sm);
  }

  .cookie-input :deep(.el-textarea__inner) {
    min-height: 80px;
    padding: var(--spacing-sm);
  }

  .login-btn {
    height: 44px;
    font-size: var(--text-sm);
  }
}

/* 表单验证错误样式 */
:deep(.el-form-item__error) {
  color: var(--color-error);
  font-size: var(--text-xs);
  margin-top: var(--spacing-xs);
}
</style>
