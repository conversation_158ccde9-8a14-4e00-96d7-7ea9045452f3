import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.DEV ? 'http://localhost:8011/api' : '/api',
  timeout: 600000, // 10分钟超时
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('请先登录')
          // 跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时（超过10分钟），请稍后重试')
    } else {
      ElMessage.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 传统认证方法
  validate: (cookies) => api.post('/auth/validate', { cookies }),
  autoValidate: () => api.get('/auth/auto-validate'),
  getSession: () => api.get('/auth/session'),
  logout: () => api.post('/auth/logout'),

  // Cookie配置管理
  createCookieConfig: (configData) => api.post('/auth/cookie-configs', configData),

  // Cookie配置认证
  authenticateWithPassword: (password) =>
    api.post('/auth/authenticate-with-password', { password }),

  // 密码和安全
  validatePasswordStrength: (password) =>
    api.post('/auth/validate-password-strength', { password }),
  generatePassword: (options = {}) =>
    api.post('/auth/generate-password', options)
}

// 商户相关API
export const merchantAPI = {
  // 获取商户信息
  getInfo: (merchantId) => api.get(`/merchant/info/${merchantId}`),
  
  // 更新商户信息
  updateInfo: (merchantId, data) => api.put(`/merchant/info/${merchantId}`, data),
  
  // 获取门店列表
  getStores: (merchantId) => api.get(`/merchant/${merchantId}/stores`),
  
  // 测试认证状态
  testAuth: (merchantId) => api.get(`/merchant/${merchantId}/test-auth`)
}

// 数据相关API
export const dataAPI = {
  // 获取会话顾客当天信息
  getChatGroups: (filters) => api.post('/data/chat-groups', filters),

  // 提取顾客详细信息（重新设计的三步流程）
  extractDetails: () => api.post('/data/extract'),

  // 获取历史数据
  getHistory: (params) => api.get('/data/history', { params }),

  // 获取统计信息
  getStats: (params) => api.get('/data/stats', { params }),

  // 删除数据
  delete: (id) => api.delete(`/data/${id}`),

  // 批量删除数据
  batchDelete: (ids) => api.post('/data/batch-delete', { ids })
}

// 导出相关API
export const exportAPI = {
  // 导出Excel文件
  exportExcel: (filters) => {
    return api.post('/export/excel', filters, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    })
  },

  // 获取导出统计
  getStats: () => api.get('/export/stats'),

  // 获取店铺列表
  getShops: () => api.get('/export/shops'),

  // 提取数据（前端生成Excel）- 单个店铺
  extractData: (shopId, fromDate, toDate) => api.post('/export/extract-data', { shopId, fromDate, toDate }),

  // 预览导出数据
  preview: (filters) => api.post('/export/preview', filters),

  // 获取导出历史
  getHistory: (params) => api.get('/export/history', { params }),

  // 重新下载历史数据
  redownload: (exportTime) => {
    return api.post('/export/redownload', { exportTime }, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }
    })
  }
}

export default api
