/* 完全自定义CSS - 无Tailwind依赖 */

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f9fafb;
}

#app {
  min-height: 100vh;
}

/* 通用工具类 */
.container {
  max-width: 28rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.min-h-screen {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.block {
  display: block;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.bg-white {
  background-color: #ffffff;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-900 {
  background-color: #111827;
}

.text-white {
  color: #ffffff;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-900 {
  color: #111827;
}

.border-2 {
  border-width: 2px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-12 {
  margin-top: 3rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.w-24 {
  width: 6rem;
}

.h-24 {
  height: 6rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.flex-1 {
  flex: 1 1 0%;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-500 {
  transition-duration: 500ms;
}

/* 移除所有hover状态 */

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  animation: fadeIn 0.5s ease-in-out;
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  background: #111827;
  border-color: #111827;
  color: #ffffff;
  border-radius: 0.5rem;
}

/* 移除Element Plus按钮hover状态 */

.el-input__wrapper {
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
}

.el-textarea__inner {
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.el-textarea__inner:focus {
  border-color: #111827;
  box-shadow: 0 0 0 3px rgba(17, 24, 39, 0.1);
}

.el-form-item__error {
  color: #ef4444;
  font-size: 12px;
}

/* 颜色工具类 */
.bg-blue-100 {
  background-color: #dbeafe;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.bg-green-500 {
  background-color: #22c55e;
}

.bg-purple-500 {
  background-color: #a855f7;
}

.bg-orange-500 {
  background-color: #f97316;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-gray-400 {
  color: #9ca3af;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.cursor-pointer {
  cursor: pointer;
}

/* 移除卡片悬停效果 */

/* 按钮特殊样式 */
.function-button {
  display: block;
  width: 100%;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  transition: all 0.2s;
  cursor: pointer;
}

/* 移除按钮悬停效果 */

.function-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载动画的边框颜色 */
.border-blue-500 {
  border-color: #3b82f6;
}

.border-green-500 {
  border-color: #22c55e;
}

.border-purple-500 {
  border-color: #a855f7;
}

.border-orange-500 {
  border-color: #f97316;
}

/* 统一的卡片样式 - 无hover效果 */
.card {
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: none;
}

/* 统一的按钮样式 - 无hover效果 */
.btn {
  background-color: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  cursor: pointer;
  transition: none;
  width: 100%;
  display: block;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
