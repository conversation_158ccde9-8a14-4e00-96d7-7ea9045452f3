import { defineStore } from 'pinia'
import { authAPI } from '../api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isLoggedIn: false,
    merchantId: null,
    merchantName: null,
    currentConfigId: null,
    currentConfigName: null,
    loading: false,
    cookieConfigs: [],
    lastLoginTime: null
  }),

  getters: {
    isAuthenticated: (state) => state.isLoggedIn && state.merchantId,
    hasMultipleConfigs: (state) => state.cookieConfigs.length > 1,
    currentConfig: (state) => {
      if (!state.currentConfigId) return null
      return state.cookieConfigs.find(config => config.id === state.currentConfigId)
    }
  },

  actions: {
    // 检查登录状态
    async checkAuth() {
      try {
        this.loading = true
        const result = await authAPI.getSession()

        if (result.success && result.isAuthenticated) {
          this.isLoggedIn = true
          this.merchantId = result.merchantId
          this.currentConfigId = result.currentConfigId
          this.currentConfigName = result.currentConfigName
        } else {
          this.clearAuth()
        }
      } catch (error) {
        console.error('检查认证状态失败:', error)
        this.clearAuth()
      } finally {
        this.loading = false
      }
    },

    // 加载Cookie配置列表
    async loadCookieConfigs(merchantId = null) {
      try {
        const result = await authAPI.getCookieConfigs(merchantId)
        if (result.success) {
          this.cookieConfigs = result.data
          return { success: true, data: result.data }
        } else {
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('加载Cookie配置失败:', error)
        return { success: false, message: '加载Cookie配置失败' }
      }
    },

    // 自动验证已保存的认证信息（支持多Cookie配置）
    async autoValidate() {
      try {
        this.loading = true
        const result = await authAPI.autoValidate()

        if (result.success && result.isAuthenticated) {
          this.isLoggedIn = true
          this.merchantId = result.merchantId
          this.merchantName = result.merchantName
          return { success: true, message: result.message }
        } else {
          this.clearAuth()
          // 返回额外信息用于前端处理
          return {
            success: false,
            message: result.message,
            hasConfigs: result.hasConfigs || false,
            optimalConfig: result.optimalConfig || null
          }
        }
      } catch (error) {
        console.error('自动验证失败:', error)
        this.clearAuth()
        return { success: false, message: '自动验证失败' }
      } finally {
        this.loading = false
      }
    },

    // 使用Cookie配置登录
    async loginWithConfig(configId, password) {
      try {
        this.loading = true
        const result = await authAPI.authenticateWithConfig(configId, password)

        if (result.success) {
          this.isLoggedIn = true
          this.merchantId = result.merchantId
          this.merchantName = result.merchantName
          this.currentConfigId = configId
          this.currentConfigName = result.configName
          this.lastLoginTime = new Date()

          return { success: true, message: result.message }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('配置登录失败:', error)
        return { success: false, message: error.message || '配置登录失败' }
      } finally {
        this.loading = false
      }
    },

    // 使用密码登录（自动匹配配置）
    async loginWithPassword(password) {
      try {
        this.loading = true
        const result = await authAPI.authenticateWithPassword(password)

        if (result.success) {
          this.isLoggedIn = true
          this.merchantId = result.merchantId
          this.merchantName = result.merchantName
          this.currentConfigId = result.currentConfigId
          this.currentConfigName = result.configName
          this.lastLoginTime = new Date()

          return { success: true, message: result.message }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('密码登录失败:', error)
        return { success: false, message: error.message || '密码登录失败' }
      } finally {
        this.loading = false
      }
    },

    // 创建Cookie配置并登录
    async createAndLoginWithConfig(configData) {
      try {
        this.loading = true

        // 先创建配置
        const createResult = await authAPI.createCookieConfig(configData)
        if (!createResult.success) {
          throw new Error(createResult.message)
        }

        // 然后使用新配置登录
        const loginResult = await this.loginWithConfig(createResult.data.id, configData.password)

        if (loginResult.success) {
          // 重新加载配置列表
          await this.loadCookieConfigs()
          return { success: true, message: '配置创建成功并已登录' }
        } else {
          throw new Error(loginResult.message)
        }
      } catch (error) {
        console.error('创建配置并登录失败:', error)
        return { success: false, message: error.message || '创建配置失败' }
      } finally {
        this.loading = false
      }
    },

    // 登录（简化版）
    async login(loginData) {
      try {
        this.loading = true

        const { cookies } = loginData

        // 验证Cookie信息
        const result = await authAPI.validate(cookies)

        if (result.success) {
          this.isLoggedIn = true
          this.merchantId = result.merchantId

          return { success: true, message: '登录成功' }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('登录失败:', error)
        return { success: false, message: error.message || '登录失败' }
      } finally {
        this.loading = false
      }
    },

    // 退出登录
    async logout() {
      try {
        await authAPI.logout()
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        this.clearAuth()
      }
    },

    // Cookie配置管理方法
    async updateCookieConfig(configId, updateData, password) {
      try {
        const result = await authAPI.updateCookieConfig(configId, updateData, password)
        if (result.success) {
          // 重新加载配置列表
          await this.loadCookieConfigs()
          return { success: true, message: result.message }
        } else {
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('更新Cookie配置失败:', error)
        return { success: false, message: '更新Cookie配置失败' }
      }
    },

    async deleteCookieConfig(configId, password) {
      try {
        const result = await authAPI.deleteCookieConfig(configId, password)
        if (result.success) {
          // 重新加载配置列表
          await this.loadCookieConfigs()

          // 如果删除的是当前使用的配置，清除当前配置信息
          if (this.currentConfigId === configId) {
            this.currentConfigId = null
            this.currentConfigName = null
          }

          return { success: true, message: result.message }
        } else {
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('删除Cookie配置失败:', error)
        return { success: false, message: '删除Cookie配置失败' }
      }
    },

    async validateCookieConfig(configId) {
      try {
        const result = await authAPI.validateCookieConfig(configId)
        if (result.success) {
          // 更新本地配置状态
          const config = this.cookieConfigs.find(c => c.id === configId)
          if (config) {
            config.isValid = result.data.isValid
            config.lastValidated = result.data.lastValidated
            config.validationError = result.data.validationError
          }
          return { success: true, data: result.data }
        } else {
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('验证Cookie配置失败:', error)
        return { success: false, message: '验证Cookie配置失败' }
      }
    },

    async getRecentlyUsedConfigs(limit = 5) {
      try {
        const result = await authAPI.getRecentlyUsedConfigs(limit)
        return result
      } catch (error) {
        console.error('获取最近使用配置失败:', error)
        return { success: false, message: '获取最近使用配置失败' }
      }
    },

    async batchValidateConfigs(configIds) {
      try {
        const result = await authAPI.batchValidateCookieConfigs(configIds)
        if (result.success) {
          // 更新本地配置状态
          result.data.forEach(item => {
            if (item.success && item.data) {
              const config = this.cookieConfigs.find(c => c.id === item.configId)
              if (config) {
                config.isValid = item.data.isValid
                config.lastValidated = item.data.lastValidated
                config.validationError = item.data.validationError
              }
            }
          })
          return result
        } else {
          return { success: false, message: result.message }
        }
      } catch (error) {
        console.error('批量验证配置失败:', error)
        return { success: false, message: '批量验证配置失败' }
      }
    },

    // 切换到指定配置（需要密码验证）
    async switchToConfig(configId, password) {
      try {
        this.loading = true
        const result = await this.loginWithConfig(configId, password)

        if (result.success) {
          return { success: true, message: '切换配置成功' }
        } else {
          return result
        }
      } catch (error) {
        console.error('切换配置失败:', error)
        return { success: false, message: '切换配置失败' }
      } finally {
        this.loading = false
      }
    },

    // 获取当前配置的详细信息
    getCurrentConfigInfo() {
      if (!this.currentConfigId) return null

      const config = this.cookieConfigs.find(c => c.id === this.currentConfigId)
      return config ? {
        id: config.id,
        name: config.name,
        merchantName: config.merchantName,
        lastUsed: config.lastUsed,
        isValid: config.isValid
      } : null
    },

    // 清除认证信息
    clearAuth() {
      this.isLoggedIn = false
      this.merchantId = null
      this.merchantName = null
      this.currentConfigId = null
      this.currentConfigName = null
      this.lastLoginTime = null
      // 保留cookieConfigs，用户可能需要重新选择
    },

    // 完全重置状态（包括配置列表）
    resetAll() {
      this.clearAuth()
      this.cookieConfigs = []
    }
  }
})
