# 点评数据项目 - dianping.bssclub.com
# HTTP配置 - 重定向到HTTPS
server {
    listen 80;
    server_name dianping.bssclub.com www.dianping.bssclub.com;

    # 重定向到HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS配置
server {
    listen 443 ssl http2;
    server_name dianping.bssclub.com www.dianping.bssclub.com;

    # SSL证书配置 (请替换为实际证书路径)
    ssl_certificate /root/certs/dianping.bssclub.com-cert.pem;
    ssl_certificate_key /root/certs/dianping.bssclub.com-key.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 其他安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 字符集
    charset utf-8;

    # 访问日志
    access_log /www/wwwlogs/dianping.bssclub.com.access.log;
    error_log /www/wwwlogs/dianping.bssclub.com.error.log;

    # API接口路由
    location /api/ {
        proxy_pass http://127.0.0.1:8011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置 - 数据提取需要更长时间
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;

        # 缓冲区设置 - API接口优化
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 16 128k;
        proxy_busy_buffers_size 256k;
        proxy_temp_file_write_size 256k;

        # 允许跨域 (前端需要)
        add_header Access-Control-Allow-Origin "https://dianping.bssclub.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        add_header Access-Control-Allow-Credentials "true" always;

        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            return 200;
        }
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        access_log off;
    }

    # 反向代理到Node.js应用
    location / {
        # 代理到点评数据后端服务 (端口8011)
        proxy_pass http://127.0.0.1:8011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置 - 数据提取需要更长时间
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;

        # 缓冲区设置 - 优化大数据传输
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 16 128k;
        proxy_busy_buffers_size 256k;
        proxy_temp_file_write_size 256k;

        # WebSocket支持 (如果需要)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 处理大文件上传
        client_max_body_size 10M;
    }

    # 静态资源缓存优化 (由Node.js应用提供)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:8011;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 超时设置 - 静态资源保持较短超时
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问敏感文件
    location ~* \.(env|log|ini|conf|bak|sql|md)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

}

# 可选：负载均衡配置 (如果有多个Node.js实例)
# upstream dianpingtart_backend {
#     server 127.0.0.1:8011 weight=1 max_fails=3 fail_timeout=30s;
#     server 127.0.0.1:3045 weight=1 max_fails=3 fail_timeout=30s;
#     keepalive 32;
# }
